{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/hero.jsx"], "sourcesContent": ["\"use client\";\n\nimport { Button } from \"@/components/ui/button\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { useEffect, useRef } from \"react\";\n\nconst HeroSection = () => {\n  const imageRef = useRef(null);\n\n  useEffect(() => {\n    const imageElement = imageRef.current;\n\n    const handleScroll = () => {\n      const scrollPosition = window.scrollY;\n      const scrollThreshold = 100;\n\n      if (scrollPosition > scrollThreshold) {\n        imageElement.classList.add(\"scrolled\");\n      } else {\n        imageElement.classList.remove(\"scrolled\");\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  return (\n    <section className=\"pt-32 pb-20 px-4 bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden\">\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n      <div className=\"container mx-auto text-center relative z-10\">\n        <div className=\"mb-6\">\n          <span className=\"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold\">\n            🚀 AI-Powered Financial Management\n          </span>\n        </div>\n        <h1 className=\"text-5xl md:text-7xl lg:text-8xl pb-6 gradient-title leading-tight\">\n          Smart Finance <br /> Management with AI\n        </h1>\n        <p className=\"text-xl text-gray-600 mb-10 max-w-3xl mx-auto leading-relaxed\">\n          Transform your financial life with our AI-powered platform that helps you track,\n          analyze, and optimize your spending with intelligent insights and automated tools.\n        </p>\n        <div className=\"flex flex-col sm:flex-row justify-center gap-4 mb-12\">\n          <Link href=\"/dashboard\">\n            <Button size=\"lg\" className=\"px-8 py-4 text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300\">\n              Start Free Trial\n            </Button>\n          </Link>\n          <Button size=\"lg\" variant=\"outline\" className=\"px-8 py-4 text-lg border-2 border-blue-600 text-blue-600 hover:bg-blue-50 transition-all duration-300\">\n            Watch Demo\n          </Button>\n        </div>\n        <div className=\"hero-image-wrapper mt-8 md:mt-12\">\n          <div ref={imageRef} className=\"hero-image\">\n            <Image\n              src=\"/banar.jpg\"\n              width={1280}\n              height={720}\n              alt=\"AIFin Dashboard Preview\"\n              className=\"rounded-2xl shadow-2xl border border-gray-200 mx-auto ring-1 ring-gray-200/50\"\n              priority\n            />\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,cAAc;IAClB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,SAAS,OAAO;QAErC,MAAM,eAAe;YACnB,MAAM,iBAAiB,OAAO,OAAO;YACrC,MAAM,kBAAkB;YAExB,IAAI,iBAAiB,iBAAiB;gBACpC,aAAa,SAAS,CAAC,GAAG,CAAC;YAC7B,OAAO;gBACL,aAAa,SAAS,CAAC,MAAM,CAAC;YAChC;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAsF;;;;;;;;;;;kCAIxG,8OAAC;wBAAG,WAAU;;4BAAqE;0CACnE,8OAAC;;;;;4BAAK;;;;;;;kCAEtB,8OAAC;wBAAE,WAAU;kCAAgE;;;;;;kCAI7E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAA+J;;;;;;;;;;;0CAI7L,8OAAC,2HAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAQ;gCAAU,WAAU;0CAAwG;;;;;;;;;;;;kCAIxJ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,KAAK;4BAAU,WAAU;sCAC5B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,KAAI;gCACJ,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;uCAEe", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/next/src/shared/lib/image-external.tsx"], "sourcesContent": ["import type { ImageConfigComplete, ImageLoaderProps } from './image-config'\nimport type { ImageProps, ImageLoader, StaticImageData } from './get-img-props'\n\nimport { getImgProps } from './get-img-props'\nimport { Image } from '../../client/image-component'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\n\n/**\n * For more advanced use cases, you can call `getImageProps()`\n * to get the props that would be passed to the underlying `<img>` element,\n * and instead pass to them to another component, style, canvas, etc.\n *\n * Read more: [Next.js docs: `getImageProps`](https://nextjs.org/docs/app/api-reference/components/image#getimageprops)\n */\nexport function getImageProps(imgProps: ImageProps) {\n  const { props } = getImgProps(imgProps, {\n    defaultLoader,\n    // This is replaced by webpack define plugin\n    imgConf: process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete,\n  })\n  // Normally we don't care about undefined props because we pass to JSX,\n  // but this exported function could be used by the end user for anything\n  // so we delete undefined props to clean it up a little.\n  for (const [key, value] of Object.entries(props)) {\n    if (value === undefined) {\n      delete props[key as keyof typeof props]\n    }\n  }\n  return { props }\n}\n\nexport default Image\n\nexport type { ImageProps, ImageLoaderProps, ImageLoader, StaticImageData }\n"], "names": ["getImageProps", "imgProps", "props", "getImgProps", "defaultLoader", "imgConf", "process", "env", "__NEXT_IMAGE_OPTS", "key", "value", "Object", "entries", "undefined", "Image"], "mappings": ";;;;;;;;;;;;;;;IAiCA,OAAoB,EAAA;eAApB;;IAjBgBA,aAAa,EAAA;eAAbA;;;;6BAbY;gCACN;sEAGI;AASnB,SAASA,cAAcC,QAAoB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGC,CAAAA,GAAAA,aAAAA,WAAW,EAACF,UAAU;QACtCG,eAAAA,aAAAA,OAAa;QACb,4CAA4C;QAC5CC,OAAAA,EAASC,QAAQC,GAAG,CAACC,iBAAiB;IACxC;IACA,uEAAuE;IACvE,wEAAwE;IACxE,wDAAwD;IACxD,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACV,OAAQ;QAChD,IAAIQ,UAAUG,WAAW;YACvB,OAAOX,KAAK,CAACO,IAA0B;QACzC;IACF;IACA,OAAO;QAAEP;IAAM;AACjB;MAEA,WAAeY,gBAAAA,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/next/image.js"], "sourcesContent": ["module.exports = require('./dist/shared/lib/image-external')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}