generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id            String    @id @default(uuid())
  clerkUserId   String    @unique // clerk user id
  email         String    @unique
  name          String?
  imageUrl      String?
  transactions  Transaction[]
  accounts      Account[]
  budgets       Budget[]
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@map("users")
}

model Account {
  id           String        @id @default(uuid())
  name         String
  type         AccountType
  balance      Decimal       @default(0) // will ask inital balance while creating an account
  isDefault    Boolean       @default(false)
  userId       String
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions Transaction[]
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  @@index([userId])
  @@map("accounts")
}

model Transaction {
  id                String            @id @default(uuid())
  type             TransactionType
  amount           Decimal
  description      String?
  date             DateTime
  category         String           
  receiptUrl       String?
  isRecurring      Boolean           @default(false)
  recurringInterval RecurringInterval? // Only used if isRecurring is true
  nextRecurringDate DateTime?         // Next date for recurring transaction
  lastProcessed    DateTime?         // Last time this recurring transaction was processed
  status           TransactionStatus  @default(COMPLETED)
  userId           String
  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  accountId        String
  account          Account           @relation(fields: [accountId], references: [id], onDelete: Cascade)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt

  @@index([userId])
  @@index([accountId])
  @@map("transactions")
}


model Budget {
  id          String       @id @default(uuid())
  amount      Decimal
  lastAlertSent DateTime?  // Track when the last alert was sent
  userId      String       @unique
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  @@index([userId])
  @@map("budgets")
}

enum TransactionType {
  INCOME
  EXPENSE
}

enum AccountType {
  CURRENT
  SAVINGS
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
}

enum RecurringInterval {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
}