@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

html {
  scroll-behavior: smooth;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.75rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .gradient {
    @apply bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600;
  }
  .gradient-title {
    @apply gradient font-extrabold tracking-tighter pr-2 pb-2 text-transparent bg-clip-text;
  }
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }
}

.hero-image-wrapper {
  perspective: 1000px;
}

.hero-image {
  /* transform: rotateX(20deg) scale(0.9) translateY(-50); */
  transform: rotateX(15deg) scale(1);
  transition: transform 0.5s ease-out;
  will-change: transform;
}

.hero-image.scrolled {
  transform: rotateX(0deg) scale(1) translateY(40px);
}

@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Add this class */
.animate-gradient {
  background-size: 200% 200%;
  animation: gradientMove 3s ease infinite;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .hero-image {
    transform: rotateX(10deg) scale(0.95);
  }

  .hero-image.scrolled {
    transform: rotateX(0deg) scale(1) translateY(20px);
  }
}

/* Mobile-first responsive utilities */
@layer utilities {
  .text-responsive {
    @apply text-sm sm:text-base md:text-lg;
  }

  .heading-responsive {
    @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
  }

  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
}
