{"node": {"7fc009b51f4c5e2ee17c430a8158571716aac73af0": {"workers": {"app/(auth)/sign-up/[[...sign-up]]/page": {"moduleId": "[project]/.next-internal/server/app/(auth)/sign-up/[[...sign-up]]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(auth)/sign-up/[[...sign-up]]/page": "action-browser"}}}, "edge": {}}