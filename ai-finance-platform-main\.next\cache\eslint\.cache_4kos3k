[{"C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(auth)\\layout.js": "1", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(auth)\\sign-in\\[[...sign-in]]\\page.jsx": "2", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(auth)\\sign-up\\[[...sign-up]]\\page.jsx": "3", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\account\\[id]\\page.jsx": "4", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\account\\_components\\account-chart.jsx": "5", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\account\\_components\\no-pagination-transaction-table.jsx": "6", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\account\\_components\\transaction-table.jsx": "7", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\dashboard\\layout.js": "8", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\dashboard\\page.jsx": "9", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\dashboard\\_components\\account-card.jsx": "10", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\dashboard\\_components\\budget-progress.jsx": "11", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\dashboard\\_components\\transaction-overview.jsx": "12", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\layout.js": "13", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\transaction\\create\\page.jsx": "14", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\transaction\\_components\\recipt-scanner.jsx": "15", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\transaction\\_components\\transaction-form.jsx": "16", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\api\\inngest\\route.js": "17", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\api\\seed\\route.js": "18", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\layout.js": "19", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\lib\\schema.js": "20", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\not-found.jsx": "21", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\page.js": "22", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\create-account-drawer.jsx": "23", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\header.jsx": "24", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\hero.jsx": "25", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\badge.jsx": "26", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\button.jsx": "27", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\calendar.jsx": "28", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\card.jsx": "29", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\checkbox.jsx": "30", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\drawer.jsx": "31", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\dropdown-menu.jsx": "32", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\input.jsx": "33", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\popover.jsx": "34", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\progress.jsx": "35", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\select.jsx": "36", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\sonner.jsx": "37", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\switch.jsx": "38", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\table.jsx": "39", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\tooltip.jsx": "40", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\arcjet.js": "41", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\checkUser.js": "42", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\inngest\\client.js": "43", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\inngest\\function.js": "44", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\prisma.js": "45", "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\utils.js": "46"}, {"size": 140, "mtime": 1742578923648, "results": "47", "hashOfConfig": "48"}, {"size": 97, "mtime": 1742578923717, "results": "49", "hashOfConfig": "48"}, {"size": 97, "mtime": 1742578923783, "results": "50", "hashOfConfig": "48"}, {"size": 2118, "mtime": 1743648553203, "results": "51", "hashOfConfig": "48"}, {"size": 5237, "mtime": 1745811804773, "results": "52", "hashOfConfig": "48"}, {"size": 5398, "mtime": 1743648273470, "results": "53", "hashOfConfig": "48"}, {"size": 15261, "mtime": 1745812812889, "results": "54", "hashOfConfig": "48"}, {"size": 539, "mtime": 1742578924152, "results": "55", "hashOfConfig": "48"}, {"size": 1990, "mtime": 1743046609870, "results": "56", "hashOfConfig": "48"}, {"size": 2524, "mtime": 1743648635837, "results": "57", "hashOfConfig": "48"}, {"size": 4191, "mtime": 1743648707059, "results": "58", "hashOfConfig": "48"}, {"size": 6962, "mtime": 1743656521132, "results": "59", "hashOfConfig": "48"}, {"size": 166, "mtime": 1742578923842, "results": "60", "hashOfConfig": "48"}, {"size": 1016, "mtime": 1743183310846, "results": "61", "hashOfConfig": "48"}, {"size": 1895, "mtime": 1742578924418, "results": "62", "hashOfConfig": "48"}, {"size": 10585, "mtime": 1743649162907, "results": "63", "hashOfConfig": "48"}, {"size": 443, "mtime": 1742578924582, "results": "64", "hashOfConfig": "48"}, {"size": 159, "mtime": 1742578924634, "results": "65", "hashOfConfig": "48"}, {"size": 954, "mtime": 1743002154308, "results": "66", "hashOfConfig": "48"}, {"size": 1043, "mtime": 1742578924684, "results": "67", "hashOfConfig": "48"}, {"size": 599, "mtime": 1742578923524, "results": "68", "hashOfConfig": "48"}, {"size": 4759, "mtime": 1743002682353, "results": "69", "hashOfConfig": "48"}, {"size": 5643, "mtime": 1742578924735, "results": "70", "hashOfConfig": "48"}, {"size": 2454, "mtime": 1743007910871, "results": "71", "hashOfConfig": "48"}, {"size": 2043, "mtime": 1743923719778, "results": "72", "hashOfConfig": "48"}, {"size": 990, "mtime": 1742578924872, "results": "73", "hashOfConfig": "48"}, {"size": 1710, "mtime": 1742578924912, "results": "74", "hashOfConfig": "48"}, {"size": 2743, "mtime": 1742578924951, "results": "75", "hashOfConfig": "48"}, {"size": 1440, "mtime": 1742578924990, "results": "76", "hashOfConfig": "48"}, {"size": 894, "mtime": 1742578925028, "results": "77", "hashOfConfig": "48"}, {"size": 2359, "mtime": 1742578925066, "results": "78", "hashOfConfig": "48"}, {"size": 6171, "mtime": 1742578925107, "results": "79", "hashOfConfig": "48"}, {"size": 690, "mtime": 1742578925150, "results": "80", "hashOfConfig": "48"}, {"size": 1180, "mtime": 1742578925193, "results": "81", "hashOfConfig": "48"}, {"size": 743, "mtime": 1742578925235, "results": "82", "hashOfConfig": "48"}, {"size": 4670, "mtime": 1742578925278, "results": "83", "hashOfConfig": "48"}, {"size": 799, "mtime": 1742578925317, "results": "84", "hashOfConfig": "48"}, {"size": 1039, "mtime": 1742578925358, "results": "85", "hashOfConfig": "48"}, {"size": 2231, "mtime": 1742578925400, "results": "86", "hashOfConfig": "48"}, {"size": 1091, "mtime": 1742578925443, "results": "87", "hashOfConfig": "48"}, {"size": 430, "mtime": 1742578925695, "results": "88", "hashOfConfig": "48"}, {"size": 717, "mtime": 1742578925737, "results": "89", "hashOfConfig": "48"}, {"size": 254, "mtime": 1744427812477, "results": "90", "hashOfConfig": "48"}, {"size": 10698, "mtime": 1744428307822, "results": "91", "hashOfConfig": "48"}, {"size": 469, "mtime": 1742578925779, "results": "92", "hashOfConfig": "48"}, {"size": 137, "mtime": 1742578925837, "results": "93", "hashOfConfig": "48"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bp6kvc", {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(auth)\\layout.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(auth)\\sign-in\\[[...sign-in]]\\page.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(auth)\\sign-up\\[[...sign-up]]\\page.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\account\\[id]\\page.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\account\\_components\\account-chart.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\account\\_components\\no-pagination-transaction-table.jsx", ["232", "233", "234", "235", "236", "237", "238", "239", "240", "241", "242", "243", "244", "245", "246", "247", "248", "249", "250", "251", "252", "253", "254", "255", "256", "257", "258", "259", "260", "261", "262", "263", "264"], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\account\\_components\\transaction-table.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\dashboard\\layout.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\dashboard\\page.jsx", ["265"], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\dashboard\\_components\\account-card.jsx", ["266", "267"], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\dashboard\\_components\\budget-progress.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\dashboard\\_components\\transaction-overview.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\layout.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\transaction\\create\\page.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\transaction\\_components\\recipt-scanner.jsx", ["268"], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\(main)\\transaction\\_components\\transaction-form.jsx", ["269"], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\api\\inngest\\route.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\api\\seed\\route.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\layout.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\lib\\schema.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\not-found.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\app\\page.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\create-account-drawer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\header.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\hero.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\badge.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\button.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\calendar.jsx", ["270", "271"], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\card.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\checkbox.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\drawer.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\dropdown-menu.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\input.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\popover.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\progress.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\select.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\sonner.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\switch.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\table.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\components\\ui\\tooltip.jsx", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\arcjet.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\checkUser.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\inngest\\client.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\inngest\\function.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\prisma.js", [], [], "C:\\Users\\<USER>\\Documents\\SEM 6 NEW Project\\2 AIFin\\AIfin\\ai-finance-platform-main\\lib\\utils.js", [], [], {"ruleId": "272", "severity": 1, "message": "273", "line": 7, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 7, "endColumn": 17}, {"ruleId": "272", "severity": 1, "message": "276", "line": 8, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 8, "endColumn": 8}, {"ruleId": "272", "severity": 1, "message": "277", "line": 9, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 9, "endColumn": 9}, {"ruleId": "272", "severity": 1, "message": "278", "line": 10, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 10, "endColumn": 4}, {"ruleId": "272", "severity": 1, "message": "279", "line": 11, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 11, "endColumn": 12}, {"ruleId": "272", "severity": 1, "message": "280", "line": 12, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 12, "endColumn": 8}, {"ruleId": "272", "severity": 1, "message": "281", "line": 14, "column": 10, "nodeType": "274", "messageId": "275", "endLine": 14, "endColumn": 16}, {"ruleId": "272", "severity": 1, "message": "282", "line": 19, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 19, "endColumn": 12}, {"ruleId": "272", "severity": 1, "message": "283", "line": 20, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 20, "endColumn": 12}, {"ruleId": "272", "severity": 1, "message": "284", "line": 25, "column": 10, "nodeType": "274", "messageId": "275", "endLine": 25, "endColumn": 15}, {"ruleId": "272", "severity": 1, "message": "285", "line": 27, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 27, "endColumn": 9}, {"ruleId": "272", "severity": 1, "message": "286", "line": 28, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 28, "endColumn": 16}, {"ruleId": "272", "severity": 1, "message": "287", "line": 29, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 29, "endColumn": 13}, {"ruleId": "272", "severity": 1, "message": "288", "line": 30, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 30, "endColumn": 16}, {"ruleId": "272", "severity": 1, "message": "289", "line": 31, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 31, "endColumn": 14}, {"ruleId": "272", "severity": 1, "message": "290", "line": 34, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 34, "endColumn": 15}, {"ruleId": "272", "severity": 1, "message": "291", "line": 35, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 35, "endColumn": 22}, {"ruleId": "272", "severity": 1, "message": "292", "line": 36, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 36, "endColumn": 19}, {"ruleId": "272", "severity": 1, "message": "293", "line": 37, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 37, "endColumn": 22}, {"ruleId": "272", "severity": 1, "message": "294", "line": 38, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 38, "endColumn": 24}, {"ruleId": "272", "severity": 1, "message": "295", "line": 41, "column": 10, "nodeType": "274", "messageId": "275", "endLine": 41, "endColumn": 16}, {"ruleId": "272", "severity": 1, "message": "296", "line": 43, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 43, "endColumn": 10}, {"ruleId": "272", "severity": 1, "message": "297", "line": 44, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 44, "endColumn": 17}, {"ruleId": "272", "severity": 1, "message": "298", "line": 45, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 45, "endColumn": 18}, {"ruleId": "272", "severity": 1, "message": "299", "line": 46, "column": 3, "nodeType": "274", "messageId": "275", "endLine": 46, "endColumn": 17}, {"ruleId": "272", "severity": 1, "message": "300", "line": 48, "column": 10, "nodeType": "274", "messageId": "275", "endLine": 48, "endColumn": 15}, {"ruleId": "272", "severity": 1, "message": "301", "line": 49, "column": 10, "nodeType": "274", "messageId": "275", "endLine": 49, "endColumn": 12}, {"ruleId": "272", "severity": 1, "message": "302", "line": 50, "column": 10, "nodeType": "274", "messageId": "275", "endLine": 50, "endColumn": 24}, {"ruleId": "272", "severity": 1, "message": "303", "line": 56, "column": 7, "nodeType": "274", "messageId": "275", "endLine": 56, "endColumn": 26}, {"ruleId": "272", "severity": 1, "message": "304", "line": 72, "column": 9, "nodeType": "274", "messageId": "275", "endLine": 72, "endColumn": 15}, {"ruleId": "272", "severity": 1, "message": "305", "line": 131, "column": 9, "nodeType": "274", "messageId": "275", "endLine": 131, "endColumn": 21}, {"ruleId": "272", "severity": 1, "message": "306", "line": 153, "column": 9, "nodeType": "274", "messageId": "275", "endLine": 153, "endColumn": 25}, {"ruleId": "272", "severity": 1, "message": "307", "line": 164, "column": 9, "nodeType": "274", "messageId": "275", "endLine": 164, "endColumn": 27}, {"ruleId": "272", "severity": 1, "message": "308", "line": 1, "column": 10, "nodeType": "274", "messageId": "275", "endLine": 1, "endColumn": 18}, {"ruleId": "272", "severity": 1, "message": "309", "line": 3, "column": 40, "nodeType": "274", "messageId": "275", "endLine": 3, "endColumn": 50}, {"ruleId": "272", "severity": 1, "message": "300", "line": 5, "column": 10, "nodeType": "274", "messageId": "275", "endLine": 5, "endColumn": 15}, {"ruleId": "310", "severity": 1, "message": "311", "line": 33, "column": 6, "nodeType": "312", "endLine": 33, "endColumn": 39, "suggestions": "313"}, {"ruleId": "310", "severity": 1, "message": "314", "line": 121, "column": 6, "nodeType": "312", "endLine": 121, "endColumn": 55, "suggestions": "315"}, {"ruleId": "272", "severity": 1, "message": "316", "line": 60, "column": 25, "nodeType": "274", "messageId": "275", "endLine": 60, "endColumn": 30}, {"ruleId": "272", "severity": 1, "message": "316", "line": 61, "column": 26, "nodeType": "274", "messageId": "275", "endLine": 61, "endColumn": 31}, "no-unused-vars", "'MoreHorizontal' is defined but never used.", "Identifier", "unusedVar", "'Trash' is defined but never used.", "'Search' is defined but never used.", "'X' is defined but never used.", "'RefreshCw' is defined but never used.", "'Clock' is defined but never used.", "'format' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'Input' is defined but never used.", "'Select' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", "'DropdownMenu' is defined but never used.", "'DropdownMenuContent' is defined but never used.", "'DropdownMenuItem' is defined but never used.", "'DropdownMenuTrigger' is defined but never used.", "'DropdownMenuSeparator' is defined but never used.", "'Button' is defined but never used.", "'Tooltip' is defined but never used.", "'TooltipContent' is defined but never used.", "'TooltipProvider' is defined but never used.", "'TooltipTrigger' is defined but never used.", "'Badge' is defined but never used.", "'cn' is defined but never used.", "'categoryColors' is defined but never used.", "'RECURRING_INTERVALS' is assigned a value but never used.", "'router' is assigned a value but never used.", "'handleSelect' is assigned a value but never used.", "'handleBulkDelete' is assigned a value but never used.", "'handleClearFilters' is assigned a value but never used.", "'Suspense' is defined but never used.", "'CreditCard' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'onScanComplete'. Either include it or remove the dependency array. If 'onScanComplete' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["317"], "React Hook useEffect has missing dependencies: 'reset' and 'router'. Either include them or remove the dependency array.", ["318"], "'props' is defined but never used.", {"desc": "319", "fix": "320"}, {"desc": "321", "fix": "322"}, "Update the dependencies array to be: [onScanComplete, scanReceiptLoading, scannedData]", {"range": "323", "text": "324"}, "Update the dependencies array to be: [transactionResult, transactionLoading, editMode, reset, router]", {"range": "325", "text": "326"}, [857, 890], "[onScanComplete, scanReceiptLoading, scannedData]", [3394, 3443], "[transactionResult, transactionLoading, editMode, reset, router]"]