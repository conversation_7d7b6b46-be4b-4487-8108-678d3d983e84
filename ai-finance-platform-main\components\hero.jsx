"use client";

import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef } from "react";

const HeroSection = () => {
  const imageRef = useRef(null);

  useEffect(() => {
    const imageElement = imageRef.current;

    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const scrollThreshold = 100;

      if (scrollPosition > scrollThreshold) {
        imageElement.classList.add("scrolled");
      } else {
        imageElement.classList.remove("scrolled");
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <section className="pt-32 pb-20 px-4 bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="container mx-auto text-center relative z-10">
        <div className="mb-6">
          <span className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">
            🚀 AI-Powered Financial Management
          </span>
        </div>
        <h1 className="text-5xl md:text-7xl lg:text-8xl pb-6 gradient-title leading-tight">
          Smart Finance <br /> Management with AI
        </h1>
        <p className="text-xl text-gray-600 mb-10 max-w-3xl mx-auto leading-relaxed">
          Transform your financial life with our AI-powered platform that helps you track,
          analyze, and optimize your spending with intelligent insights and automated tools.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
          <Link href="/dashboard">
            <Button size="lg" className="px-8 py-4 text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300">
              Start Free Trial
            </Button>
          </Link>
          <Button size="lg" variant="outline" className="px-8 py-4 text-lg border-2 border-blue-600 text-blue-600 hover:bg-blue-50 transition-all duration-300">
            Watch Demo
          </Button>
        </div>
        <div className="hero-image-wrapper mt-8 md:mt-12">
          <div ref={imageRef} className="hero-image">
            <Image
              src="/banar.jpg"
              width={1280}
              height={720}
              alt="AIFin Dashboard Preview"
              className="rounded-2xl shadow-2xl border border-gray-200 mx-auto ring-1 ring-gray-200/50"
              priority
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
