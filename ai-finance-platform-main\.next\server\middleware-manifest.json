{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_61ee5cf8._.js", "server/edge/chunks/node_modules_@bufbuild_protobuf_dist_esm_e8606cf1._.js", "server/edge/chunks/node_modules_@arcjet_protocol_0c492877._.js", "server/edge/chunks/node_modules_@connectrpc_connect_dist_esm_2e5be2d1._.js", "server/edge/chunks/node_modules_@clerk_backend_dist_c2a6f0fe._.js", "server/edge/chunks/node_modules_4f3a07d3._.js", "server/edge/chunks/[root of the server]__20c9a702._.js", "server/edge/chunks/edge-wrapper_2627ac34.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [{"name": "wasm_node_modules__arcjet_analyze_wasm_arcjet_analyze_js_req_component_core_325b988e", "filePath": "server/edge/chunks/node_modules_@arcjet_analyze_wasm_arcjet_analyze_js_req_component_core_325b988e.wasm"}, {"name": "wasm_node_modules__arcjet_analyze_wasm_arcjet_analyze_js_req_component_core2_325b988e", "filePath": "server/edge/chunks/node_modules_@arcjet_analyze_wasm_arcjet_analyze_js_req_component_core2_325b988e.wasm"}, {"name": "wasm_node_modules__arcjet_analyze_wasm_arcjet_analyze_js_req_component_core3_325b988e", "filePath": "server/edge/chunks/node_modules_@arcjet_analyze_wasm_arcjet_analyze_js_req_component_core3_325b988e.wasm"}], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GNQz0lVyaaIc7f0c4yGD06irgeCKLswqReDQDriS71I=", "__NEXT_PREVIEW_MODE_ID": "cb076cd12c770bb7eee8d2f06d805ce9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "69c55189f78e081ec4df6c8678366068f507de5b0281eb92ffc2566a530bd84f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1c104e9d4e0cad167f5e7e079241d222b42ad410ff89ac2c7b3d8bd8eeebfa0a"}}}, "sortedMiddleware": ["/"], "functions": {}}