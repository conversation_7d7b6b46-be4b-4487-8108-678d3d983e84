import Header from "@/components/header";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { Inter } from "next/font/google";
import { Toaster } from "sonner";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "AIFin",
  description: "One stop Finance Platform",
};

export default function RootLayout({ children }) {
  return (
    <ClerkProvider>
      <html lang="en">
        <head>
          <link rel="icon" href="/logo.jpg" sizes="any" />
        </head>
        <body className={`${inter.className}`}>
          <Header />
          <main className="min-h-screen">{children}</main>
          <Toaster richColors />

          <footer className="bg-gradient-to-br from-gray-50 to-blue-50 py-16 border-t border-gray-200">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
                <div className="col-span-1 md:col-span-2">
                  <h3 className="text-2xl font-bold gradient-title mb-4">AIFin</h3>
                  <p className="text-gray-600 mb-4 max-w-md">
                    Transform your financial life with AI-powered insights and automated tools for smarter money management.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Product</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li><a href="#features" className="hover:text-blue-600 transition-colors">Features</a></li>
                    <li><a href="#" className="hover:text-blue-600 transition-colors">Pricing</a></li>
                    <li><a href="#" className="hover:text-blue-600 transition-colors">Security</a></li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Company</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li><a href="#" className="hover:text-blue-600 transition-colors">About</a></li>
                    <li><a href="#" className="hover:text-blue-600 transition-colors">Contact</a></li>
                    <li><a href="#" className="hover:text-blue-600 transition-colors">Privacy</a></li>
                  </ul>
                </div>
              </div>
              <div className="border-t border-gray-200 pt-8 text-center text-gray-600">
                <p>© 2024 AIFin. Made with 💗 for better financial management.</p>
              </div>
            </div>
          </footer>
        </body>
      </html>
    </ClerkProvider>
  );
}
