{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/actions/budget.js"], "sourcesContent": ["\"use server\";\n\nimport { db } from \"@/lib/prisma\";\nimport { auth } from \"@clerk/nextjs/server\";\nimport { revalidatePath } from \"next/cache\";\n\nexport async function getCurrentBudget(accountId) {\n  try {\n    const { userId } = await auth();\n    if (!userId) throw new Error(\"Unauthorized\");\n\n    const user = await db.user.findUnique({\n      where: { clerkUserId: userId },\n    });\n\n    if (!user) {\n      throw new Error(\"User not found\");\n    }\n\n    const budget = await db.budget.findFirst({\n      where: {\n        userId: user.id,\n      },\n    });\n\n    // Get current month's expenses\n    const currentDate = new Date();\n    const startOfMonth = new Date(\n      currentDate.getFullYear(),\n      currentDate.getMonth(),\n      1\n    );\n    const endOfMonth = new Date(\n      currentDate.getFullYear(),\n      currentDate.getMonth() + 1,\n      0\n    );\n\n    const expenses = await db.transaction.aggregate({\n      where: {\n        userId: user.id,\n        type: \"EXPENSE\",\n        date: {\n          gte: startOfMonth,\n          lte: endOfMonth,\n        },\n        accountId,\n      },\n      _sum: {\n        amount: true,\n      },\n    });\n\n    return {\n      budget: budget ? { ...budget, amount: budget.amount.toNumber() } : null,\n      currentExpenses: expenses._sum.amount\n        ? expenses._sum.amount.toNumber()\n        : 0,\n    };\n  } catch (error) {\n    console.error(\"Error fetching budget:\", error);\n    throw error;\n  }\n}\n\nexport async function updateBudget(amount) {\n  try {\n    const { userId } = await auth();\n    if (!userId) throw new Error(\"Unauthorized\");\n\n    const user = await db.user.findUnique({\n      where: { clerkUserId: userId },\n    });\n\n    if (!user) throw new Error(\"User not found\");\n\n    // Update or create budget\n    const budget = await db.budget.upsert({\n      where: {\n        userId: user.id,\n      },\n      update: {\n        amount,\n      },\n      create: {\n        userId: user.id,\n        amount,\n      },\n    });\n\n    revalidatePath(\"/dashboard\");\n    return {\n      success: true,\n      data: { ...budget, amount: budget.amount.toNumber() },\n    };\n  } catch (error) {\n    console.error(\"Error updating budget:\", error);\n    return { success: false, error: error.message };\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;;;;;;AAEO,eAAe,uCAAc,GAAd,iBAAiB,SAAS;IAC9C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAI,AAAD;QAC5B,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;QAE7B,MAAM,OAAO,MAAM,6GAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE,aAAa;YAAO;QAC/B;QAEA,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,MAAM,6GAAA,CAAA,KAAE,CAAC,MAAM,CAAC,SAAS,CAAC;YACvC,OAAO;gBACL,QAAQ,KAAK,EAAE;YACjB;QACF;QAEA,+BAA+B;QAC/B,MAAM,cAAc,IAAI;QACxB,MAAM,eAAe,IAAI,KACvB,YAAY,WAAW,IACvB,YAAY,QAAQ,IACpB;QAEF,MAAM,aAAa,IAAI,KACrB,YAAY,WAAW,IACvB,YAAY,QAAQ,KAAK,GACzB;QAGF,MAAM,WAAW,MAAM,6GAAA,CAAA,KAAE,CAAC,WAAW,CAAC,SAAS,CAAC;YAC9C,OAAO;gBACL,QAAQ,KAAK,EAAE;gBACf,MAAM;gBACN,MAAM;oBACJ,KAAK;oBACL,KAAK;gBACP;gBACA;YACF;YACA,MAAM;gBACJ,QAAQ;YACV;QACF;QAEA,OAAO;YACL,QAAQ,SAAS;gBAAE,GAAG,MAAM;gBAAE,QAAQ,OAAO,MAAM,CAAC,QAAQ;YAAG,IAAI;YACnE,iBAAiB,SAAS,IAAI,CAAC,MAAM,GACjC,SAAS,IAAI,CAAC,MAAM,CAAC,QAAQ,KAC7B;QACN;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,eAAe,uCAAU,GAAV,aAAa,MAAM;IACvC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAI,AAAD;QAC5B,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;QAE7B,MAAM,OAAO,MAAM,6GAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE,aAAa;YAAO;QAC/B;QAEA,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,0BAA0B;QAC1B,MAAM,SAAS,MAAM,6GAAA,CAAA,KAAE,CAAC,MAAM,CAAC,MAAM,CAAC;YACpC,OAAO;gBACL,QAAQ,KAAK,EAAE;YACjB;YACA,QAAQ;gBACN;YACF;YACA,QAAQ;gBACN,QAAQ,KAAK,EAAE;gBACf;YACF;QACF;QAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YACL,SAAS;YACT,MAAM;gBAAE,GAAG,MAAM;gBAAE,QAAQ,OAAO,MAAM,CAAC,QAAQ;YAAG;QACtD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC;IAChD;AACF;;;IA7FsB;IA2DA;;AA3DA,+OAAA;AA2DA,+OAAA", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/lib/arcjet.js"], "sourcesContent": ["import arcjet, { tokenBucket } from \"@arcjet/next\";\n\nconst aj = arcjet({\n  key: process.env.ARCJET_KEY,\n  characteristics: [\"userId\"], // Track based on Clerk userId\n  rules: [\n    // Rate limiting specifically for collection creation\n    tokenBucket({\n      mode: \"LIVE\",\n      refillRate: 10, // 10 collections\n      interval: 3600, // per hour\n      capacity: 10, // maximum burst capacity\n    }),\n  ],\n});\n\nexport default aj;\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;;AAEA,MAAM,KAAK,CAAA,GAAA,yJAAA,CAAA,UAAM,AAAD,EAAE;IAChB,KAAK,QAAQ,GAAG,CAAC,UAAU;IAC3B,iBAAiB;QAAC;KAAS;IAC3B,OAAO;QACL,qDAAqD;QACrD,CAAA,GAAA,+IAAA,CAAA,cAAW,AAAD,EAAE;YACV,MAAM;YACN,YAAY;YACZ,UAAU;YACV,UAAU;QACZ;KACD;AACH;uCAEe", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/actions/dashboard.js"], "sourcesContent": ["\"use server\";\n\nimport aj from \"@/lib/arcjet\";\nimport { db } from \"@/lib/prisma\";\nimport { request } from \"@arcjet/next\";\nimport { auth } from \"@clerk/nextjs/server\";\nimport { revalidatePath } from \"next/cache\";\n\nconst serializeTransaction = (obj) => {\n  const serialized = { ...obj };\n  if (obj.balance) {\n    serialized.balance = obj.balance.toNumber();\n  }\n  if (obj.amount) {\n    serialized.amount = obj.amount.toNumber();\n  }\n  return serialized;\n};\n\nexport async function getUserAccounts() {\n  const { userId } = await auth();\n  if (!userId) throw new Error(\"Unauthorized\");\n\n  const user = await db.user.findUnique({\n    where: { clerkUserId: userId },\n  });\n\n  if (!user) {\n    throw new Error(\"User not found\");\n  }\n\n  try {\n    const accounts = await db.account.findMany({\n      where: { userId: user.id },\n      orderBy: { createdAt: \"desc\" },\n      include: {\n        _count: {\n          select: {\n            transactions: true,\n          },\n        },\n      },\n    });\n\n    // Serialize accounts before sending to client\n    const serializedAccounts = accounts.map(serializeTransaction);\n\n    return serializedAccounts;\n  } catch (error) {\n    console.error(error.message);\n  }\n}\n\nexport async function createAccount(data) {\n  try {\n    const { userId } = await auth();\n    if (!userId) throw new Error(\"Unauthorized\");\n\n    // Get request data for ArcJet\n    const req = await request();\n\n    // Check rate limit\n    const decision = await aj.protect(req, {\n      userId,\n      requested: 1, // Specify how many tokens to consume\n    });\n\n    if (decision.isDenied()) {\n      if (decision.reason.isRateLimit()) {\n        const { remaining, reset } = decision.reason;\n        console.error({\n          code: \"RATE_LIMIT_EXCEEDED\",\n          details: {\n            remaining,\n            resetInSeconds: reset,\n          },\n        });\n\n        throw new Error(\"Too many requests. Please try again later.\");\n      }\n\n      throw new Error(\"Request blocked\");\n    }\n\n    const user = await db.user.findUnique({\n      where: { clerkUserId: userId },\n    });\n\n    if (!user) {\n      throw new Error(\"User not found\");\n    }\n\n    // Convert balance to float before saving\n    const balanceFloat = parseFloat(data.balance);\n    if (isNaN(balanceFloat)) {\n      throw new Error(\"Invalid balance amount\");\n    }\n\n    // Check if this is the user's first account\n    const existingAccounts = await db.account.findMany({\n      where: { userId: user.id },\n    });\n\n    // If it's the first account, make it default regardless of user input\n    // If not, use the user's preference\n    const shouldBeDefault =\n      existingAccounts.length === 0 ? true : data.isDefault;\n\n    // If this account should be default, unset other default accounts\n    if (shouldBeDefault) {\n      await db.account.updateMany({\n        where: { userId: user.id, isDefault: true },\n        data: { isDefault: false },\n      });\n    }\n\n    // Create new account\n    const account = await db.account.create({\n      data: {\n        ...data,\n        balance: balanceFloat,\n        userId: user.id,\n        isDefault: shouldBeDefault, // Override the isDefault based on our logic\n      },\n    });\n\n    // Serialize the account before returning\n    const serializedAccount = serializeTransaction(account);\n\n    revalidatePath(\"/dashboard\");\n    return { success: true, data: serializedAccount };\n  } catch (error) {\n    throw new Error(error.message);\n  }\n}\n\nexport async function getDashboardData() {\n  const { userId } = await auth();\n  if (!userId) throw new Error(\"Unauthorized\");\n\n  const user = await db.user.findUnique({\n    where: { clerkUserId: userId },\n  });\n\n  if (!user) {\n    throw new Error(\"User not found\");\n  }\n\n  // Get all user transactions\n  const transactions = await db.transaction.findMany({\n    where: { userId: user.id },\n    orderBy: { date: \"desc\" },\n  });\n\n  return transactions.map(serializeTransaction);\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;AAEA,MAAM,uBAAuB,CAAC;IAC5B,MAAM,aAAa;QAAE,GAAG,GAAG;IAAC;IAC5B,IAAI,IAAI,OAAO,EAAE;QACf,WAAW,OAAO,GAAG,IAAI,OAAO,CAAC,QAAQ;IAC3C;IACA,IAAI,IAAI,MAAM,EAAE;QACd,WAAW,MAAM,GAAG,IAAI,MAAM,CAAC,QAAQ;IACzC;IACA,OAAO;AACT;AAEO,eAAe,uCAAa,GAAb;IACpB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAI,AAAD;IAC5B,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;IAE7B,MAAM,OAAO,MAAM,6GAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;QACpC,OAAO;YAAE,aAAa;QAAO;IAC/B;IAEA,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,6GAAA,CAAA,KAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;YACzC,OAAO;gBAAE,QAAQ,KAAK,EAAE;YAAC;YACzB,SAAS;gBAAE,WAAW;YAAO;YAC7B,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,cAAc;oBAChB;gBACF;YACF;QACF;QAEA,8CAA8C;QAC9C,MAAM,qBAAqB,SAAS,GAAG,CAAC;QAExC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,MAAM,OAAO;IAC7B;AACF;AAEO,eAAe,uCAAW,GAAX,cAAc,IAAI;IACtC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAI,AAAD;QAC5B,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;QAE7B,8BAA8B;QAC9B,MAAM,MAAM,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD;QAExB,mBAAmB;QACnB,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAE,CAAC,OAAO,CAAC,KAAK;YACrC;YACA,WAAW;QACb;QAEA,IAAI,SAAS,QAAQ,IAAI;YACvB,IAAI,SAAS,MAAM,CAAC,WAAW,IAAI;gBACjC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,SAAS,MAAM;gBAC5C,QAAQ,KAAK,CAAC;oBACZ,MAAM;oBACN,SAAS;wBACP;wBACA,gBAAgB;oBAClB;gBACF;gBAEA,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,6GAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE,aAAa;YAAO;QAC/B;QAEA,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,yCAAyC;QACzC,MAAM,eAAe,WAAW,KAAK,OAAO;QAC5C,IAAI,MAAM,eAAe;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,4CAA4C;QAC5C,MAAM,mBAAmB,MAAM,6GAAA,CAAA,KAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;YACjD,OAAO;gBAAE,QAAQ,KAAK,EAAE;YAAC;QAC3B;QAEA,sEAAsE;QACtE,oCAAoC;QACpC,MAAM,kBACJ,iBAAiB,MAAM,KAAK,IAAI,OAAO,KAAK,SAAS;QAEvD,kEAAkE;QAClE,IAAI,iBAAiB;YACnB,MAAM,6GAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC1B,OAAO;oBAAE,QAAQ,KAAK,EAAE;oBAAE,WAAW;gBAAK;gBAC1C,MAAM;oBAAE,WAAW;gBAAM;YAC3B;QACF;QAEA,qBAAqB;QACrB,MAAM,UAAU,MAAM,6GAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;YACtC,MAAM;gBACJ,GAAG,IAAI;gBACP,SAAS;gBACT,QAAQ,KAAK,EAAE;gBACf,WAAW;YACb;QACF;QAEA,yCAAyC;QACzC,MAAM,oBAAoB,qBAAqB;QAE/C,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,OAAO;YAAE,SAAS;YAAM,MAAM;QAAkB;IAClD,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM,MAAM,OAAO;IAC/B;AACF;AAEO,eAAe,uCAAc,GAAd;IACpB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAI,AAAD;IAC5B,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;IAE7B,MAAM,OAAO,MAAM,6GAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;QACpC,OAAO;YAAE,aAAa;QAAO;IAC/B;IAEA,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,4BAA4B;IAC5B,MAAM,eAAe,MAAM,6GAAA,CAAA,KAAE,CAAC,WAAW,CAAC,QAAQ,CAAC;QACjD,OAAO;YAAE,QAAQ,KAAK,EAAE;QAAC;QACzB,SAAS;YAAE,MAAM;QAAO;IAC1B;IAEA,OAAO,aAAa,GAAG,CAAC;AAC1B;;;IAxIsB;IAkCA;IAmFA;;AArHA,+OAAA;AAkCA,+OAAA;AAmFA,+OAAA", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/create-account-drawer.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CreateAccountDrawer = registerClientReference(\n    function() { throw new Error(\"Attempted to call CreateAccountDrawer() from the server but CreateAccountDrawer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/create-account-drawer.jsx <module evaluation>\",\n    \"CreateAccountDrawer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,sEACA", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/create-account-drawer.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CreateAccountDrawer = registerClientReference(\n    function() { throw new Error(\"Attempted to call CreateAccountDrawer() from the server but CreateAccountDrawer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/create-account-drawer.jsx\",\n    \"CreateAccountDrawer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,kDACA", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/card.jsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"rounded-xl border bg-card text-card-foreground shadow\", className)}\n    {...props} />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props} />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props} />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACtD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;;;;;;AAEb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACjE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/_components/account-card.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AccountCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccountCard() from the server but AccountCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/dashboard/_components/account-card.jsx <module evaluation>\",\n    \"AccountCard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,mFACA", "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/_components/account-card.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AccountCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccountCard() from the server but AccountCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/dashboard/_components/account-card.jsx\",\n    \"AccountCard\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA", "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/_components/budget-progress.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BudgetProgress = registerClientReference(\n    function() { throw new Error(\"Attempted to call BudgetProgress() from the server but BudgetProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/dashboard/_components/budget-progress.jsx <module evaluation>\",\n    \"BudgetProgress\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sFACA", "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/_components/budget-progress.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BudgetProgress = registerClientReference(\n    function() { throw new Error(\"Attempted to call BudgetProgress() from the server but BudgetProgress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/dashboard/_components/budget-progress.jsx\",\n    \"BudgetProgress\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kEACA", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/_components/transaction-overview.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DashboardOverview = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardOverview() from the server but DashboardOverview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/dashboard/_components/transaction-overview.jsx <module evaluation>\",\n    \"DashboardOverview\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2FACA", "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/_components/transaction-overview.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DashboardOverview = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardOverview() from the server but DashboardOverview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/dashboard/_components/transaction-overview.jsx\",\n    \"DashboardOverview\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,uEACA", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/page.jsx"], "sourcesContent": ["\nimport { getCurrentBudget } from \"@/actions/budget\";\nimport { getDashboardData, getUserAccounts } from \"@/actions/dashboard\";\nimport { CreateAccountDrawer } from \"@/components/create-account-drawer\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Plus } from \"lucide-react\";\nimport { AccountCard } from \"./_components/account-card\";\nimport { BudgetProgress } from \"./_components/budget-progress\";\nimport { DashboardOverview } from \"./_components/transaction-overview\";\n\nexport default async function DashboardPage() {\n  const [accounts, transactions] = await Promise.all([\n    getUserAccounts(),\n    getDashboardData(),\n  ]);\n\n  const defaultAccount = accounts?.find((account) => account.isDefault);\n\n  // Get budget for default account\n  let budgetData = null;\n  if (defaultAccount) {\n    budgetData = await getCurrentBudget(defaultAccount.id);\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Budget Progress */}\n      <BudgetProgress\n        initialBudget={budgetData?.budget}\n        currentExpenses={budgetData?.currentExpenses || 0}\n      />\n\n      {/* Dashboard Overview */}\n      <DashboardOverview\n        accounts={accounts}\n        transactions={transactions || []}\n      />\n\n      {/* Accounts Grid */}\n      <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n        <CreateAccountDrawer>\n          <Card className=\"hover:shadow-md transition-shadow cursor-pointer border-dashed\">\n            <CardContent className=\"flex flex-col items-center justify-center text-muted-foreground h-full pt-5\">\n              <Plus className=\"h-10 w-10 mb-2\" />\n              <p className=\"text-sm font-medium\">Add New Account</p>\n            </CardContent>\n          </Card>\n        </CreateAccountDrawer>\n        {accounts.length > 0 &&\n          accounts?.map((account) => (\n            <AccountCard key={account.id} account={account} />\n          ))}\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,eAAe;IAC5B,MAAM,CAAC,UAAU,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;QACjD,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD;QACd,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD;KAChB;IAED,MAAM,iBAAiB,UAAU,KAAK,CAAC,UAAY,QAAQ,SAAS;IAEpE,iCAAiC;IACjC,IAAI,aAAa;IACjB,IAAI,gBAAgB;QAClB,aAAa,MAAM,CAAA,GAAA,iHAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe,EAAE;IACvD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,kKAAA,CAAA,iBAAc;gBACb,eAAe,YAAY;gBAC3B,iBAAiB,YAAY,mBAAmB;;;;;;0BAIlD,8OAAC,uKAAA,CAAA,oBAAiB;gBAChB,UAAU;gBACV,cAAc,gBAAgB,EAAE;;;;;;0BAIlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0IAAA,CAAA,sBAAmB;kCAClB,cAAA,8OAAC,yHAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;oBAIxC,SAAS,MAAM,GAAG,KACjB,UAAU,IAAI,CAAC,wBACb,8OAAC,+JAAA,CAAA,cAAW;4BAAkB,SAAS;2BAArB,QAAQ,EAAE;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/layout.js"], "sourcesContent": ["import DashboardPage from \"./page\";\nimport { BarLoader } from \"react-spinners\";\nimport { Suspense } from \"react\";\n\nexport default function Layout() {\n  return (\n    <div className=\"px-5\">\n      <div className=\"flex items-center justify-between mb-5\">\n        <h1 className=\"text-6xl font-bold tracking-tight gradient-title\">\n          Dashboard\n        </h1>\n      </div>\n      <Suspense\n        fallback={<BarLoader className=\"mt-4\" width={\"100%\"} color=\"#9333ea\" />}\n      >\n        <DashboardPage />\n      </Suspense>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAmD;;;;;;;;;;;0BAInE,8OAAC,qMAAA,CAAA,WAAQ;gBACP,wBAAU,8OAAC,6LAAA,CAAA,YAAS;oBAAC,WAAU;oBAAO,OAAO;oBAAQ,OAAM;;;;;;0BAE3D,cAAA,8OAAC,qIAAA,CAAA,UAAa;;;;;;;;;;;;;;;;AAItB", "debugId": null}}]}