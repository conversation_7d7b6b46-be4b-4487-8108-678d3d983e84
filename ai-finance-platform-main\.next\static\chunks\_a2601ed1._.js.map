{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/button.jsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nconst Button = React.forwardRef(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/hero.jsx"], "sourcesContent": ["\"use client\";\n\nimport { Button } from \"@/components/ui/button\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { useEffect, useRef } from \"react\";\n\nconst HeroSection = () => {\n  const imageRef = useRef(null);\n\n  useEffect(() => {\n    const imageElement = imageRef.current;\n\n    const handleScroll = () => {\n      const scrollPosition = window.scrollY;\n      const scrollThreshold = 100;\n\n      if (scrollPosition > scrollThreshold) {\n        imageElement.classList.add(\"scrolled\");\n      } else {\n        imageElement.classList.remove(\"scrolled\");\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  return (\n    <section className=\"pt-32 pb-20 px-4 bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden\">\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n      <div className=\"container mx-auto text-center relative z-10\">\n        <div className=\"mb-6\">\n          <span className=\"inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold\">\n            🚀 AI-Powered Financial Management\n          </span>\n        </div>\n        <h1 className=\"text-5xl md:text-7xl lg:text-8xl pb-6 gradient-title leading-tight\">\n          Smart Finance <br /> Management with AI\n        </h1>\n        <p className=\"text-xl text-gray-600 mb-10 max-w-3xl mx-auto leading-relaxed\">\n          Transform your financial life with our AI-powered platform that helps you track,\n          analyze, and optimize your spending with intelligent insights and automated tools.\n        </p>\n        <div className=\"flex flex-col sm:flex-row justify-center gap-4 mb-12\">\n          <Link href=\"/dashboard\">\n            <Button size=\"lg\" className=\"px-8 py-4 text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300\">\n              Start Free Trial\n            </Button>\n          </Link>\n          <Button size=\"lg\" variant=\"outline\" className=\"px-8 py-4 text-lg border-2 border-blue-600 text-blue-600 hover:bg-blue-50 transition-all duration-300\">\n            Watch Demo\n          </Button>\n        </div>\n        <div className=\"hero-image-wrapper mt-8 md:mt-12\">\n          <div ref={imageRef} className=\"hero-image\">\n            <Image\n              src=\"/banar.jpg\"\n              width={1280}\n              height={720}\n              alt=\"AIFin Dashboard Preview\"\n              className=\"rounded-2xl shadow-2xl border border-gray-200 mx-auto ring-1 ring-gray-200/50\"\n              priority\n            />\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,cAAc;;IAClB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,eAAe,SAAS,OAAO;YAErC,MAAM;sDAAe;oBACnB,MAAM,iBAAiB,OAAO,OAAO;oBACrC,MAAM,kBAAkB;oBAExB,IAAI,iBAAiB,iBAAiB;wBACpC,aAAa,SAAS,CAAC,GAAG,CAAC;oBAC7B,OAAO;wBACL,aAAa,SAAS,CAAC,MAAM,CAAC;oBAChC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;gCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAsF;;;;;;;;;;;kCAIxG,6LAAC;wBAAG,WAAU;;4BAAqE;0CACnE,6LAAC;;;;;4BAAK;;;;;;;kCAEtB,6LAAC;wBAAE,WAAU;kCAAgE;;;;;;kCAI7E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAA+J;;;;;;;;;;;0CAI7L,6LAAC,8HAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAQ;gCAAU,WAAU;0CAAwG;;;;;;;;;;;;kCAIxJ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,KAAK;4BAAU,WAAU;sCAC5B,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,KAAI;gCACJ,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GA9DM;KAAA;uCAgES", "debugId": null}}]}