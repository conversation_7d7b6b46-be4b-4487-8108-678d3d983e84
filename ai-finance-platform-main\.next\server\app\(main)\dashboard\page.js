const CHUNK_PUBLIC_PATH = "server/app/(main)/dashboard/page.js";
const runtime = require("../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_5855a896._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__3c00be2f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ezheaders_dist_index_mjs_536c1f17._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_b4b52cbe._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__a0fe7b43._.js");
runtime.loadChunk("server/chunks/ssr/app_not-found_jsx_edf1eb2a._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_67ccf2ab._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/app_(main)_layout_cb7b4e98.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_not-found-error_1119763b.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_13a58921._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@bufbuild_protobuf_dist_esm_aba5ed9b._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@arcjet_protocol_dd835343._.js");
runtime.loadChunk("server/chunks/ssr/dd92d_modules_@arcjet_analyze__virtual_arcjet_analyze_js_req_component_core_fd8fb239.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@arcjet_analyze__virtual_b66f0378._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@arcjet_analyze_f37c3406._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@connectrpc_connect_dist_esm_83234621._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_undici_8a43bb8f._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_1aa98d6f._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__53101858._.js");
runtime.loadChunk("server/chunks/ssr/_571131ed._.js");
runtime.loadChunk("server/chunks/ssr/_7380b6f5._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/(main)/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/actions/budget.js [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/actions/dashboard.js [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/actions/account.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/(main)/dashboard/page { MODULE_0 => \"[project]/app/layout.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/app/not-found.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/(main)/layout.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/app/(main)/dashboard/layout.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_9 => \"[project]/app/(main)/dashboard/page.jsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/(main)/dashboard/page { MODULE_0 => \"[project]/app/layout.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/app/not-found.jsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/(main)/layout.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/app/(main)/dashboard/layout.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_9 => \"[project]/app/(main)/dashboard/page.jsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
