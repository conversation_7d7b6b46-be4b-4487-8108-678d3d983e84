{"version": 3, "sources": [], "sections": [{"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/hero.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/hero.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/hero.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/hero.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/hero.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/hero.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmQ,GAChS,iCACA", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/card.jsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"rounded-xl border bg-card text-card-foreground shadow\", className)}\n    {...props} />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props} />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props} />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACtD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;;;;;;AAEb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACjE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/data/landing.js"], "sourcesContent": ["import {\n  BarChart3,\n  Receipt,\n  <PERSON><PERSON><PERSON>,\n  CreditCard,\n  Globe,\n  Zap,\n} from \"lucide-react\";\n\n// Stats Data\nexport const statsData = [\n  {\n    value: \"50K+\",\n    label: \"Active Users\",\n  },\n  {\n    value: \"$2B+\",\n    label: \"Transactions Tracked\",\n  },\n  {\n    value: \"99.9%\",\n    label: \"Uptime\",\n  },\n  {\n    value: \"4.9/5\",\n    label: \"User Rating\",\n  },\n];\n\n// Features Data\nexport const featuresData = [\n  {\n    icon: <BarChart3 className=\"h-8 w-8 text-blue-600\" />,\n    title: \"Advanced Analytics\",\n    description:\n      \"Get detailed insights into your spending patterns with AI-powered analytics\",\n  },\n  {\n    icon: <Receipt className=\"h-8 w-8 text-blue-600\" />,\n    title: \"Smart Receipt Scanner\",\n    description:\n      \"Extract data automatically from receipts using advanced AI technology\",\n  },\n  {\n    icon: <PieChart className=\"h-8 w-8 text-blue-600\" />,\n    title: \"Budget Planning\",\n    description: \"Create and manage budgets with intelligent recommendations\",\n  },\n  {\n    icon: <CreditCard className=\"h-8 w-8 text-blue-600\" />,\n    title: \"Multi-Account Support\",\n    description: \"Manage multiple accounts and credit cards in one place\",\n  },\n  {\n    icon: <Globe className=\"h-8 w-8 text-blue-600\" />,\n    title: \"Multi-Currency\",\n    description: \"Support for multiple currencies with real-time conversion\",\n  },\n  {\n    icon: <Zap className=\"h-8 w-8 text-blue-600\" />,\n    title: \"Automated Insights\",\n    description: \"Get automated financial insights and recommendations\",\n  },\n];\n\n// How It Works Data\nexport const howItWorksData = [\n  {\n    icon: <CreditCard className=\"h-8 w-8 text-blue-600\" />,\n    title: \"1. Create Your Account\",\n    description:\n      \"Get started in minutes with our simple and secure sign-up process\",\n  },\n  {\n    icon: <BarChart3 className=\"h-8 w-8 text-blue-600\" />,\n    title: \"2. Track Your Spending\",\n    description:\n      \"Automatically categorize and track your transactions in real-time\",\n  },\n  {\n    icon: <PieChart className=\"h-8 w-8 text-blue-600\" />,\n    title: \"3. Get Insights\",\n    description:\n      \"Receive AI-powered insights and recommendations to optimize your finances\",\n  },\n];\n\n// Testimonials Data\nexport const testimonialsData = [\n  {\n    name: \"Sarah Johnson\",\n    role: \"Small Business Owner\",\n    image: \"https://randomuser.me/api/portraits/women/75.jpg\",\n    quote:\n      \"Welth has transformed how I manage my business finances. The AI insights have helped me identify cost-saving opportunities I never knew existed.\",\n  },\n  {\n    name: \"Michael Chen\",\n    role: \"Freelancer\",\n    image: \"https://randomuser.me/api/portraits/men/75.jpg\",\n    quote:\n      \"The receipt scanning feature saves me hours each month. Now I can focus on my work instead of manual data entry and expense tracking.\",\n  },\n  {\n    name: \"Emily Rodriguez\",\n    role: \"Financial Advisor\",\n    image: \"https://randomuser.me/api/portraits/women/74.jpg\",\n    quote:\n      \"I recommend Welth to all my clients. The multi-currency support and detailed analytics make it perfect for international investors.\",\n  },\n];\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAUO,MAAM,YAAY;IACvB;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;CACD;AAGM,MAAM,eAAe;IAC1B;QACE,oBAAM,8OAAC,kNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,OAAO;QACP,aACE;IACJ;IACA;QACE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,OAAO;QACP,aACE;IACJ;IACA;QACE,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,kNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC5B,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvB,OAAO;QACP,aAAa;IACf;IACA;QACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QACrB,OAAO;QACP,aAAa;IACf;CACD;AAGM,MAAM,iBAAiB;IAC5B;QACE,oBAAM,8OAAC,kNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC5B,OAAO;QACP,aACE;IACJ;IACA;QACE,oBAAM,8OAAC,kNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,OAAO;QACP,aACE;IACJ;IACA;QACE,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,OAAO;QACP,aACE;IACJ;CACD;AAGM,MAAM,mBAAmB;IAC9B;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,OACE;IACJ;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,OACE;IACJ;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;QACP,OACE;IACJ;CACD", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/page.js"], "sourcesContent": ["import HeroSection from \"@/components/hero\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport {\n    featuresData,\n    howItWorksData,\n    statsData,\n    testimonialsData,\n} from \"@/data/landing\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\n\nconst LandingPage = () => {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <HeroSection />\n\n      {/* Stats Section */}\n      <section className=\"py-20 bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n            {statsData.map((stat, index) => (\n              <div key={index} className=\"text-center\">\n                <div className=\"text-4xl font-bold text-blue-600 mb-2\">\n                  {stat.value}\n                </div>\n                <div className=\"text-gray-700 font-medium\">{stat.label}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <h2 className=\"text-4xl font-bold text-center mb-4 gradient-title\">\n            Everything you need to manage your finances\n          </h2>\n          <p className=\"text-xl text-gray-600 text-center mb-16 max-w-3xl mx-auto\">\n            Powerful AI-driven tools to help you track, analyze, and optimize your financial life\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {featuresData.map((feature, index) => (\n              <Card className=\"p-6 hover:shadow-glow transition-all duration-300 border-0 shadow-lg\" key={index}>\n                <CardContent className=\"space-y-4 pt-4\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white\">\n                    {feature.icon}\n                  </div>\n                  <h3 className=\"text-xl font-semibold text-gray-900\">{feature.title}</h3>\n                  <p className=\"text-gray-600 leading-relaxed\">{feature.description}</p>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* How It Works Section */}\n      <section className=\"py-20 bg-gradient-to-br from-blue-50 to-indigo-100\">\n        <div className=\"container mx-auto px-4\">\n          <h2 className=\"text-4xl font-bold text-center mb-4 gradient-title\">How It Works</h2>\n          <p className=\"text-xl text-gray-600 text-center mb-16 max-w-2xl mx-auto\">\n            Get started with AIFin in just three simple steps\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-12\">\n            {howItWorksData.map((step, index) => (\n              <div key={index} className=\"text-center relative\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white shadow-lg\">\n                  {step.icon}\n                </div>\n                <h3 className=\"text-xl font-semibold mb-4 text-gray-900\">{step.title}</h3>\n                <p className=\"text-gray-600 leading-relaxed\">{step.description}</p>\n                {index < howItWorksData.length - 1 && (\n                  <div className=\"hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-blue-300 to-purple-300 transform -translate-x-8\"></div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Testimonials Section */}\n      <section id=\"testimonials\" className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <h2 className=\"text-4xl font-bold text-center mb-4 gradient-title\">\n            What Our Users Say\n          </h2>\n          <p className=\"text-xl text-gray-600 text-center mb-16 max-w-2xl mx-auto\">\n            Join thousands of satisfied users who trust AIFin with their finances\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {testimonialsData.map((testimonial, index) => (\n              <Card key={index} className=\"p-6 hover:shadow-glow transition-all duration-300 border-0 shadow-lg\">\n                <CardContent className=\"pt-4\">\n                  <div className=\"flex items-center mb-4\">\n                    <Image\n                      src={testimonial.image}\n                      alt={testimonial.name}\n                      width={48}\n                      height={48}\n                      className=\"rounded-full ring-2 ring-blue-100\"\n                    />\n                    <div className=\"ml-4\">\n                      <div className=\"font-semibold text-gray-900\">{testimonial.name}</div>\n                      <div className=\"text-sm text-blue-600 font-medium\">\n                        {testimonial.role}\n                      </div>\n                    </div>\n                  </div>\n                  <p className=\"text-gray-600 leading-relaxed italic\">\"{testimonial.quote}\"</p>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-black/10\"></div>\n        <div className=\"container mx-auto px-4 text-center relative z-10\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            Ready to Take Control of Your Finances?\n          </h2>\n          <p className=\"text-blue-100 mb-8 max-w-2xl mx-auto text-lg\">\n            Join thousands of users who are already managing their finances\n            smarter with AIFin\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/dashboard\">\n              <Button\n                size=\"lg\"\n                className=\"bg-white text-blue-600 hover:bg-blue-50 font-semibold px-8 py-3 text-lg shadow-lg hover:shadow-xl transition-all duration-300\"\n              >\n                Start Free Trial\n              </Button>\n            </Link>\n            <Button\n              size=\"lg\"\n              variant=\"outline\"\n              className=\"border-white text-white hover:bg-white hover:text-blue-600 font-semibold px-8 py-3 text-lg transition-all duration-300\"\n            >\n              Learn More\n            </Button>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default LandingPage;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAMA;AACA;;;;;;;;AAEA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,mHAAA,CAAA,UAAW;;;;;0BAGZ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,+GAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAEb,8OAAC;wCAAI,WAAU;kDAA6B,KAAK,KAAK;;;;;;;+BAJ9C;;;;;;;;;;;;;;;;;;;;0BAYlB,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;sCAGzE,8OAAC;4BAAI,WAAU;sCACZ,+GAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAG,WAAU;0DAAuC,QAAQ,KAAK;;;;;;0DAClE,8OAAC;gDAAE,WAAU;0DAAiC,QAAQ,WAAW;;;;;;;;;;;;mCANuB;;;;;;;;;;;;;;;;;;;;;0BAepG,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCACnE,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;sCAGzE,8OAAC;4BAAI,WAAU;sCACZ,+GAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAG,WAAU;sDAA4C,KAAK,KAAK;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAAiC,KAAK,WAAW;;;;;;wCAC7D,QAAQ,+GAAA,CAAA,iBAAc,CAAC,MAAM,GAAG,mBAC/B,8OAAC;4CAAI,WAAU;;;;;;;mCAPT;;;;;;;;;;;;;;;;;;;;;0BAgBlB,8OAAC;gBAAQ,IAAG;gBAAe,WAAU;0BACnC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,8OAAC;4BAAE,WAAU;sCAA4D;;;;;;sCAGzE,8OAAC;4BAAI,WAAU;sCACZ,+GAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,aAAa,sBAClC,8OAAC,yHAAA,CAAA,OAAI;oCAAa,WAAU;8CAC1B,cAAA,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,YAAY,KAAK;wDACtB,KAAK,YAAY,IAAI;wDACrB,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA+B,YAAY,IAAI;;;;;;0EAC9D,8OAAC;gEAAI,WAAU;0EACZ,YAAY,IAAI;;;;;;;;;;;;;;;;;;0DAIvB,8OAAC;gDAAE,WAAU;;oDAAuC;oDAAE,YAAY,KAAK;oDAAC;;;;;;;;;;;;;mCAjBjE;;;;;;;;;;;;;;;;;;;;;0BA0BnB,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;kDAIH,8OAAC,2HAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}]}