{"name": "finance-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "email": "email dev", "postinstall": "prisma generate"}, "dependencies": {"@arcjet/next": "^1.0.0-alpha.34", "@clerk/nextjs": "^6.6.0", "@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.0.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.4", "@react-email/components": "^0.0.30", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "inngest": "^3.35.0", "lucide-react": "^0.462.0", "next": "^15.0.3", "next-themes": "^0.4.3", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.53.2", "react-spinners": "^0.14.1", "recharts": "^2.14.1", "resend": "^4.1.2", "sonner": "^1.7.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "zod": "^3.23.8"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "prisma": "^6.0.1", "react-email": "3.0.3", "tailwindcss": "^3.4.1"}}