{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/shared/node_modules/swr/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n// dispatch for CommonJS interop named imports.\n\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\nvar didWarnOld18Alpha = false;\nvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  {\n    if (!didWarnOld18Alpha) {\n      if (React.startTransition !== undefined) {\n        didWarnOld18Alpha = true;\n\n        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  var value = getSnapshot();\n\n  {\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n\n      if (!objectIs(value, cachedValue)) {\n        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  var _useState = useState({\n    inst: {\n      value: value,\n      getSnapshot: getSnapshot\n    }\n  }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n\n    var handleStoreChange = function () {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst: inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange);\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  var prevValue = inst.value;\n\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\nvar isServerEnvironment = !canUseDOM;\n\nvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\nvar useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n\nexports.useSyncExternalStore = useSyncExternalStore$2;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AAEA,wCAA2C;IACzC,CAAC;QAEO;QAEV,yCAAyC,GACzC,IACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,2BAA2B,KAC/D,YACF;YACA,+BAA+B,2BAA2B,CAAC,IAAI;QACjE;QACU,IAAI;QAEd,IAAI,uBAAuB,MAAM,kDAAkD;QAEnF,SAAS,MAAM,MAAM;YACnB;gBACE;oBACE,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,IAAI,QAAQ,GAAG,QAAQ,OAAO,QAAS;wBACjH,IAAI,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC,MAAM;oBACpC;oBAEA,aAAa,SAAS,QAAQ;gBAChC;YACF;QACF;QAEA,SAAS,aAAa,KAAK,EAAE,MAAM,EAAE,IAAI;YACvC,mDAAmD;YACnD,6CAA6C;YAC7C;gBACE,IAAI,yBAAyB,qBAAqB,sBAAsB;gBACxE,IAAI,QAAQ,uBAAuB,gBAAgB;gBAEnD,IAAI,UAAU,IAAI;oBAChB,UAAU;oBACV,OAAO,KAAK,MAAM,CAAC;wBAAC;qBAAM;gBAC5B,EAAE,+DAA+D;gBAGjE,IAAI,iBAAiB,KAAK,GAAG,CAAC,SAAU,IAAI;oBAC1C,OAAO,OAAO;gBAChB,IAAI,+CAA+C;gBAEnD,eAAe,OAAO,CAAC,cAAc,SAAS,oEAAoE;gBAClH,6DAA6D;gBAC7D,gEAAgE;gBAEhE,SAAS,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS;YACzD;QACF;QAEA;;;CAGC,GACD,SAAS,GAAG,CAAC,EAAE,CAAC;YACd,OAAO,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,EAAE,sCAAsC;;QAE7G;QAEA,IAAI,WAAW,OAAO,OAAO,EAAE,KAAK,aAAa,OAAO,EAAE,GAAG;QAE7D,+CAA+C;QAE/C,IAAI,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa;QACvC,IAAI,oBAAoB;QACxB,IAAI,6BAA6B,OAAO,0EAA0E;QAClH,6EAA6E;QAC7E,6EAA6E;QAC7E,0EAA0E;QAC1E,gEAAgE;QAChE,4EAA4E;QAC5E,kBAAkB;QAClB,EAAE;QACF,8EAA8E;QAC9E,8EAA8E;QAE9E,SAAS,qBAAqB,SAAS,EAAE,WAAW,EACpD,8EAA8E;QAC9E,kEAAkE;QAClE,sBAAsB;QACtB,iBAAiB;YACf;gBACE,IAAI,CAAC,mBAAmB;oBACtB,IAAI,MAAM,eAAe,KAAK,WAAW;wBACvC,oBAAoB;wBAEpB,MAAM,mEAAmE,gDAAgD,mEAAmE;oBAC9L;gBACF;YACF;YACA,qEAAqE;YACrE,4DAA4D;YAC5D,sBAAsB;YAGtB,IAAI,QAAQ;YAEZ;gBACE,IAAI,CAAC,4BAA4B;oBAC/B,IAAI,cAAc;oBAElB,IAAI,CAAC,SAAS,OAAO,cAAc;wBACjC,MAAM;wBAEN,6BAA6B;oBAC/B;gBACF;YACF;YACA,sEAAsE;YACtE,4EAA4E;YAC5E,qBAAqB;YACrB,EAAE;YACF,4EAA4E;YAC5E,gEAAgE;YAChE,EAAE;YACF,4EAA4E;YAC5E,8EAA8E;YAC9E,6BAA6B;YAC7B,EAAE;YACF,4EAA4E;YAC5E,6CAA6C;YAG7C,IAAI,YAAY,SAAS;gBACvB,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,IACI,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,EACxB,cAAc,SAAS,CAAC,EAAE,EAAE,6EAA6E;YAC7G,wEAAwE;YACxE,wBAAwB;YAGxB,gBAAgB;gBACd,KAAK,KAAK,GAAG;gBACb,KAAK,WAAW,GAAG,aAAa,qEAAqE;gBACrG,wEAAwE;gBACxE,yEAAyE;gBACzE,qCAAqC;gBAErC,IAAI,uBAAuB,OAAO;oBAChC,qBAAqB;oBACrB,YAAY;wBACV,MAAM;oBACR;gBACF;YACF,GAAG;gBAAC;gBAAW;gBAAO;aAAY;YAClC,UAAU;gBACR,yEAAyE;gBACzE,wCAAwC;gBACxC,IAAI,uBAAuB,OAAO;oBAChC,qBAAqB;oBACrB,YAAY;wBACV,MAAM;oBACR;gBACF;gBAEA,IAAI,oBAAoB;oBACtB,0EAA0E;oBAC1E,sEAAsE;oBACtE,wEAAwE;oBACxE,+CAA+C;oBAC/C,0EAA0E;oBAC1E,uBAAuB;oBACvB,IAAI,uBAAuB,OAAO;wBAChC,qBAAqB;wBACrB,YAAY;4BACV,MAAM;wBACR;oBACF;gBACF,GAAG,yDAAyD;gBAG5D,OAAO,UAAU;YACnB,GAAG;gBAAC;aAAU;YACd,cAAc;YACd,OAAO;QACT;QAEA,SAAS,uBAAuB,IAAI;YAClC,IAAI,oBAAoB,KAAK,WAAW;YACxC,IAAI,YAAY,KAAK,KAAK;YAE1B,IAAI;gBACF,IAAI,YAAY;gBAChB,OAAO,CAAC,SAAS,WAAW;YAC9B,EAAE,OAAO,OAAO;gBACd,OAAO;YACT;QACF;QAEA,SAAS,uBAAuB,SAAS,EAAE,WAAW,EAAE,iBAAiB;YACvE,4EAA4E;YAC5E,8EAA8E;YAC9E,kEAAkE;YAClE,sBAAsB;YACtB,OAAO;QACT;QAEA,IAAI,YAAY,CAAC,CAAC,CAAC,OAAO,WAAW,eAAe,OAAO,OAAO,QAAQ,KAAK,eAAe,OAAO,OAAO,QAAQ,CAAC,aAAa,KAAK,WAAW;QAElJ,IAAI,sBAAsB,CAAC;QAE3B,IAAI,OAAO,sBAAsB,yBAAyB;QAC1D,IAAI,yBAAyB,MAAM,oBAAoB,KAAK,YAAY,MAAM,oBAAoB,GAAG;QAErG,QAAQ,oBAAoB,GAAG;QACrB,yCAAyC,GACnD,IACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,0BAA0B,KAC9D,YACF;YACA,+BAA+B,0BAA0B,CAAC,IAAI;QAChE;IAEE,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/shared/node_modules/swr/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/dequal/dist/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "names": [], "mappings": ";;;AAAA,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAEzC,SAAS,KAAK,IAAI,EAAE,GAAG,EAAE,GAAG;IAC3B,KAAK,OAAO,KAAK,IAAI,GAAI;QACxB,IAAI,OAAO,KAAK,MAAM,OAAO;IAC9B;AACD;AAEO,SAAS,OAAO,GAAG,EAAE,GAAG;IAC9B,IAAI,MAAM,KAAK;IACf,IAAI,QAAQ,KAAK,OAAO;IAExB,IAAI,OAAO,OAAO,CAAC,OAAK,IAAI,WAAW,MAAM,IAAI,WAAW,EAAE;QAC7D,IAAI,SAAS,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,OAAO;QACvD,IAAI,SAAS,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ;QAE3D,IAAI,SAAS,OAAO;YACnB,IAAI,CAAC,MAAI,IAAI,MAAM,MAAM,IAAI,MAAM,EAAE;gBACpC,MAAO,SAAS,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI;YAC1C;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM;gBACN,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,OAAO;YAC3B;YACA,OAAO;QACR;QAEA,IAAI,SAAS,KAAK;YACjB,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;gBAC1B,OAAO;YACR;YACA,KAAK,OAAO,IAAK;gBAChB,MAAM,GAAG,CAAC,EAAE;gBACZ,IAAI,OAAO,OAAO,QAAQ,UAAU;oBACnC,MAAM,KAAK,KAAK;oBAChB,IAAI,CAAC,KAAK,OAAO;gBAClB;gBACA,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,OAAO;oBAClC,OAAO;gBACR;YACD;YACA,OAAO;QACR;QAEA,IAAI,SAAS,aAAa;YACzB,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,WAAW;QACtB,OAAO,IAAI,SAAS,UAAU;YAC7B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;YAClD;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,YAAY,MAAM,CAAC,MAAM;YAC5B,IAAI,CAAC,MAAI,IAAI,UAAU,MAAM,IAAI,UAAU,EAAE;gBAC5C,MAAO,SAAS,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;YACtC;YACA,OAAO,QAAQ,CAAC;QACjB;QAEA,IAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;YACrC,MAAM;YACN,IAAK,QAAQ,IAAK;gBACjB,IAAI,IAAI,IAAI,CAAC,KAAK,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,KAAK,OAAO,OAAO;gBACjE,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,GAAG,OAAO;YAC7D;YACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;QACpC;IACD;IAEA,OAAO,QAAQ,OAAO,QAAQ;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/client-boundary/PromisifiedAuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@clerk/clerk-react';\nimport { useDerivedAuth } from '@clerk/clerk-react/internal';\nimport type { InitialState } from '@clerk/types';\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nconst PromisifiedAuthContext = React.createContext<Promise<InitialState> | InitialState | null>(null);\n\nexport function PromisifiedAuthProvider({\n  authPromise,\n  children,\n}: {\n  authPromise: Promise<InitialState> | InitialState;\n  children: React.ReactNode;\n}) {\n  return <PromisifiedAuthContext.Provider value={authPromise}>{children}</PromisifiedAuthContext.Provider>;\n}\n\n/**\n * Returns the current auth state, the user and session ids and the `getToken`\n * that can be used to retrieve the given template or the default Clerk token.\n *\n * Until Clerk loads, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access the `userId` and `sessionId` variables.\n *\n * For projects using NextJs or Remix, you can have immediate access to this data during SSR\n * simply by using the `ClerkProvider`.\n *\n * @example\n * A simple example:\n *\n * import { useAuth } from '@clerk/nextjs'\n *\n * function Hello() {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   if(isSignedIn) {\n *     return null;\n *   }\n *   console.log(sessionId, userId)\n *   return <div>...</div>\n * }\n *\n * @example\n * Basic example in a NextJs app. This page will be fully rendered during SSR:\n *\n * import { useAuth } from '@clerk/nextjs'\n *\n * export HelloPage = () => {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   console.log(isSignedIn, sessionId, userId)\n *   return <div>...</div>\n * }\n */\nexport function usePromisifiedAuth() {\n  const isPagesRouter = useRouter();\n  const valueFromContext = React.useContext(PromisifiedAuthContext);\n\n  let resolvedData = valueFromContext;\n  if (valueFromContext && 'then' in valueFromContext) {\n    resolvedData = React.use(valueFromContext);\n  }\n\n  // At this point we should have a usable auth object\n\n  if (typeof window === 'undefined') {\n    // Pages router should always use useAuth as it is able to grab initial auth state from context during SSR.\n    if (isPagesRouter) {\n      return useAuth();\n    }\n\n    // We don't need to deal with Clerk being loaded here\n    return useDerivedAuth(resolvedData);\n  } else {\n    return useAuth(resolvedData);\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA,SAAS,eAAe;;AACxB,SAAS,sBAAsB;AAE/B,SAAS,iBAAiB;AAC1B,OAAO,WAAW;;;;;;AAElB,MAAM,+NAAyB,UAAA,CAAM,aAAA,CAA2D,IAAI;AAE7F,SAAS,wBAAwB,EACtC,WAAA,EACA,QAAA,EACF,EAGG;IACD,OAAO,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,CAAC,uBAAuB,QAAA,EAAvB;QAAgC,OAAO;IAAA,GAAc,QAAS;AACxE;AAsCO,SAAS,qBAAqB;IACnC,MAAM,6JAAgB,YAAA,CAAU;IAChC,MAAM,yNAAmB,UAAA,CAAM,UAAA,CAAW,sBAAsB;IAEhE,IAAI,eAAe;IACnB,IAAI,oBAAoB,UAAU,kBAAkB;QAClD,qNAAe,UAAA,CAAM,GAAA,CAAI,gBAAgB;IAC3C;IAIA,IAAI,OAAO,WAAW,aAAa;QAEjC,IAAI,eAAe;YACjB,mMAAO,UAAA,CAAQ;QACjB;QAGA,mMAAO,iBAAA,EAAe,YAAY;IACpC,OAAO;QACL,mMAAO,UAAA,EAAQ,YAAY;IAC7B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useSafeLayoutEffect.tsx"], "sourcesContent": ["import React from 'react';\n\n// TODO: Import from shared once [JS-118] is done\nexport const useSafeLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n"], "names": [], "mappings": ";;;AAAA,OAAO,WAAW;;AAGX,MAAM,sBAAsB,OAAO,WAAW,oNAAc,UAAA,CAAM,eAAA,yMAAkB,UAAA,CAAM,SAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/client-boundary/NextOptionsContext.tsx"], "sourcesContent": ["import React from 'react';\n\nimport type { NextClerkProviderProps } from '../types';\n\ntype ClerkNextContextValue = Partial<Omit<NextClerkProviderProps, 'children'>>;\n\nconst ClerkNextOptionsCtx = React.createContext<{ value: ClerkNextContextValue } | undefined>(undefined);\nClerkNextOptionsCtx.displayName = 'ClerkNextOptionsCtx';\n\nconst useClerkNextOptions = () => {\n  const ctx = React.useContext(ClerkNextOptionsCtx) as { value: ClerkNextContextValue };\n  return ctx?.value;\n};\n\nconst ClerkNextOptionsProvider = (\n  props: React.PropsWithChildren<{ options: ClerkNextContextValue }>,\n): React.JSX.Element => {\n  const { children, options } = props;\n  return <ClerkNextOptionsCtx.Provider value={{ value: options }}>{children}</ClerkNextOptionsCtx.Provider>;\n};\n\nexport { ClerkNextOptionsProvider, useClerkNextOptions };\n"], "names": [], "mappings": ";;;;AAAA,OAAO,WAAW;;AAMlB,MAAM,4NAAsB,UAAA,CAAM,aAAA,CAA4D,KAAA,CAAS;AACvG,oBAAoB,WAAA,GAAc;AAElC,MAAM,sBAAsB,MAAM;IAChC,MAAM,4MAAM,UAAA,CAAM,UAAA,CAAW,mBAAmB;IAChD,OAAO,OAAA,OAAA,KAAA,IAAA,IAAK,KAAA;AACd;AAEA,MAAM,2BAA2B,CAC/B,UACsB;IACtB,MAAM,EAAE,QAAA,EAAU,OAAA,CAAQ,CAAA,GAAI;IAC9B,OAAO,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,CAAC,oBAAoB,QAAA,EAApB;QAA6B,OAAO;YAAE,OAAO;QAAQ;IAAA,GAAI,QAAS;AAC5E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/utils/clerk-js-script.tsx"], "sourcesContent": ["import { useClerk } from '@clerk/clerk-react';\nimport { buildClerkJsScriptAttributes, clerkJsScriptUrl } from '@clerk/clerk-react/internal';\nimport NextScript from 'next/script';\nimport React from 'react';\n\nimport { useClerkNextOptions } from '../client-boundary/NextOptionsContext';\n\ntype ClerkJSScriptProps = {\n  router: 'app' | 'pages';\n};\n\nfunction ClerkJSScript(props: ClerkJSScriptProps) {\n  const { publishableKey, clerkJSU<PERSON>, clerkJ<PERSON><PERSON><PERSON>, clerkJSV<PERSON>t, nonce } = useClerkNextOptions();\n  const { domain, proxyUrl } = useClerk();\n  const options = {\n    domain,\n    proxyUrl,\n    publishableKey: publishableKey!,\n    clerkJ<PERSON><PERSON>,\n    clerkJ<PERSON><PERSON><PERSON>,\n    clerkJ<PERSON><PERSON>t,\n    nonce,\n  };\n  const scriptUrl = clerkJsScriptUrl(options);\n\n  /**\n   * Notes:\n   * `next/script` in 13.x.x when used with App Router will fail to pass any of our `data-*` attributes, resulting in errors\n   * Nextjs App Router will automatically move inline scripts inside `<head/>`\n   * Using the `nextjs/script` for App Router with the `beforeInteractive` strategy will throw an error because our custom script will be mounted outside the `html` tag.\n   */\n  const Script = props.router === 'app' ? 'script' : NextScript;\n\n  return (\n    <Script\n      src={scriptUrl}\n      data-clerk-js-script\n      async\n      // `nextjs/script` will add defer by default and does not get removed when we async is true\n      defer={props.router === 'pages' ? false : undefined}\n      crossOrigin='anonymous'\n      strategy={props.router === 'pages' ? 'beforeInteractive' : undefined}\n      {...buildClerkJsScriptAttributes(options)}\n    />\n  );\n}\n\nexport { ClerkJSScript };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB;;;AACzB,SAAS,8BAA8B,wBAAwB;AAC/D,OAAO,gBAAgB;AACvB,OAAO,WAAW;AAElB,SAAS,2BAA2B;;;;;;AAMpC,SAAS,cAAc,KAAA,EAA2B;IAChD,MAAM,EAAE,cAAA,EAAgB,UAAA,EAAY,cAAA,EAAgB,cAAA,EAAgB,KAAA,CAAM,CAAA,oMAAI,sBAAA,CAAoB;IAClG,MAAM,EAAE,MAAA,EAAQ,QAAA,CAAS,CAAA,oKAAI,WAAA,CAAS;IACtC,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IACA,MAAM,gLAAY,mBAAA,EAAiB,OAAO;IAQ1C,MAAM,SAAS,MAAM,MAAA,KAAW,QAAQ,0IAAW,UAAA;IAEnD,OACE,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,CAAC,QAAA;QACC,KAAK;QACL,wBAAoB;QACpB,OAAK;QAEL,OAAO,MAAM,MAAA,KAAW,UAAU,QAAQ,KAAA;QAC1C,aAAY;QACZ,UAAU,MAAM,MAAA,KAAW,UAAU,sBAAsB,KAAA;QAC1D,uKAAG,+BAAA,EAA6B,OAAO,CAAA;IAAA;AAG9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/server/constants.ts"], "sourcesContent": ["import { apiUrlFromPublishableKey } from '@clerk/shared/apiUrlFromPublishableKey';\nimport { isTruthy } from '@clerk/shared/underscore';\n\nexport const CLERK_JS_VERSION = process.env.NEXT_PUBLIC_CLERK_JS_VERSION || '';\nexport const CLERK_JS_URL = process.env.NEXT_PUBLIC_CLERK_JS_URL || '';\nexport const API_VERSION = process.env.CLERK_API_VERSION || 'v1';\nexport const SECRET_KEY = process.env.CLERK_SECRET_KEY || '';\nexport const PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '';\nexport const ENCRYPTION_KEY = process.env.CLERK_ENCRYPTION_KEY || '';\nexport const API_URL = process.env.CLERK_API_URL || apiUrlFromPublishableKey(PUBLISHABLE_KEY);\nexport const DOMAIN = process.env.NEXT_PUBLIC_CLERK_DOMAIN || '';\nexport const PROXY_URL = process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '';\nexport const IS_SATELLITE = isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE) || false;\nexport const SIGN_IN_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '';\nexport const SIGN_UP_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '';\nexport const SDK_METADATA = {\n  name: PACKAGE_NAME,\n  version: PACKAGE_VERSION,\n  environment: process.env.NODE_ENV,\n};\n\nexport const TELEMETRY_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED);\nexport const TELEMETRY_DEBUG = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,SAAS,gCAAgC;;AACzC,SAAS,gBAAgB;;;;AAElB,MAAM,mBAAmB,QAAQ,GAAA,CAAI,4BAAA,IAAgC;AACrE,MAAM,eAAe,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AAC7D,MAAM,cAAc,QAAQ,GAAA,CAAI,iBAAA,IAAqB;AACrD,MAAM,aAAa,QAAQ,GAAA,CAAI,gBAAA,IAAoB;AACnD,MAAM,kBAAkB,QAAQ,IAAI,wFAAqC;AACzE,MAAM,iBAAiB,QAAQ,GAAA,CAAI,oBAAA,IAAwB;AAC3D,MAAM,UAAU,QAAQ,GAAA,CAAI,aAAA,KAAiB,8LAAA,EAAyB,eAAe;AACrF,MAAM,SAAS,QAAQ,GAAA,CAAI,wBAAA,IAA4B;AACvD,MAAM,YAAY,QAAQ,GAAA,CAAI,2BAAA,IAA+B;AAC7D,MAAM,mLAAe,WAAA,EAAS,QAAQ,GAAA,CAAI,8BAA8B,KAAK;AAC7E,MAAM,cAAc,QAAQ,IAAI,oCAAiC;AACjE,MAAM,cAAc,QAAQ,IAAI,oCAAiC;AACjE,MAAM,eAAe;IAC1B,MAAM;IACN,SAAS;IACT,WAAA,EAAa,QAAQ,IAAI;AAC3B;AAEO,MAAM,yLAAqB,WAAA,EAAS,QAAQ,GAAA,CAAI,oCAAoC;AACpF,MAAM,sLAAkB,WAAA,EAAS,QAAQ,GAAA,CAAI,iCAAiC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/utils/mergeNextClerkPropsWithEnv.ts"], "sourcesContent": ["import { isTruthy } from '@clerk/shared/underscore';\n\nimport { SDK_METADATA } from '../server/constants';\nimport type { NextClerkProviderProps } from '../types';\n\n// @ts-ignore - https://github.com/microsoft/TypeScript/issues/47663\nexport const mergeNextClerkPropsWithEnv = (props: Omit<NextClerkProviderProps, 'children'>): any => {\n  return {\n    ...props,\n    publishableKey: props.publishableKey || process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '',\n    clerkJSUrl: props.clerkJSUrl || process.env.NEXT_PUBLIC_CLERK_JS_URL,\n    clerkJSVersion: props.clerkJSVersion || process.env.NEXT_PUBLIC_CLERK_JS_VERSION,\n    proxyUrl: props.proxyUrl || process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '',\n    domain: props.domain || process.env.NEXT_PUBLIC_CLERK_DOMAIN || '',\n    isSatellite: props.isSatellite || isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE),\n    signInUrl: props.signInUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '',\n    signUpUrl: props.signUpUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '',\n    signInForceRedirectUrl:\n      props.signInForceRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_FORCE_REDIRECT_URL || '',\n    signUpForceRedirectUrl:\n      props.signUpForceRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL || '',\n    signInFallbackRedirectUrl:\n      props.signInFallbackRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL || '',\n    signUpFallbackRedirectUrl:\n      props.signUpFallbackRedirectUrl || process.env.NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL || '',\n    afterSignInUrl: props.afterSignInUrl || process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL || '',\n    afterSignUpUrl: props.afterSignUpUrl || process.env.NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL || '',\n    telemetry: props.telemetry ?? {\n      disabled: isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),\n      debug: isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),\n    },\n    sdkMetadata: SDK_METADATA,\n  };\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB;;AAEzB,SAAS,oBAAoB;;;AAItB,MAAM,6BAA6B,CAAC,UAAyD;IANpG,IAAA;IAOE,OAAO;QACL,GAAG,KAAA;QACH,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,IAAI,wFAAqC;QACzF,YAAY,MAAM,UAAA,IAAc,QAAQ,GAAA,CAAI,wBAAA;QAC5C,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,GAAA,CAAI,4BAAA;QACpD,UAAU,MAAM,QAAA,IAAY,QAAQ,GAAA,CAAI,2BAAA,IAA+B;QACvE,QAAQ,MAAM,MAAA,IAAU,QAAQ,GAAA,CAAI,wBAAA,IAA4B;QAChE,aAAa,MAAM,WAAA,wKAAe,WAAA,EAAS,QAAQ,GAAA,CAAI,8BAA8B;QACrF,WAAW,MAAM,SAAA,IAAa,QAAQ,IAAI,oCAAiC;QAC3E,WAAW,MAAM,SAAA,IAAa,QAAQ,IAAI,oCAAiC;QAC3E,wBACE,MAAM,sBAAA,IAA0B,QAAQ,GAAA,CAAI,4CAAA,IAAgD;QAC9F,wBACE,MAAM,sBAAA,IAA0B,QAAQ,GAAA,CAAI,4CAAA,IAAgD;QAC9F,2BACE,MAAM,yBAAA,IAA6B,QAAQ,GAAA,CAAI,+CAAA,IAAmD;QACpG,2BACE,MAAM,yBAAA,IAA6B,QAAQ,GAAA,CAAI,+CAAA,IAAmD;QACpG,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,GAAA,CAAI,mCAAA,IAAuC;QAC3F,gBAAgB,MAAM,cAAA,IAAkB,QAAQ,GAAA,CAAI,mCAAA,IAAuC;QAC3F,WAAA,CAAW,KAAA,MAAM,SAAA,KAAN,OAAA,KAAmB;YAC5B,8KAAU,WAAA,EAAS,QAAQ,GAAA,CAAI,oCAAoC;YACnE,2KAAO,WAAA,EAAS,QAAQ,GAAA,CAAI,iCAAiC;QAC/D;QACA,qLAAa,eAAA;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/dist/esm/app-router/server-actions.js"], "sourcesContent": ["\"use server\";\nimport { getCookies } from \"ezheaders\";\nasync function invalidateCacheAction() {\n  void (await getCookies()).delete(`__clerk_invalidate_cache_cookie_${Date.now()}`);\n}\nexport {\n  invalidateCacheAction\n};\n"], "names": [], "mappings": ";;;;;IAME", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/utils/removeBasePath.ts"], "sourcesContent": ["/**\n * Removes the Next.js basePath from the provided destination if set.\n * @param to Destination route to navigate to\n * @returns Destination without basePath, if set\n */\nexport function removeBasePath(to: string): string {\n  let destination = to;\n  const basePath = process.env.__NEXT_ROUTER_BASEPATH;\n  if (basePath && destination.startsWith(basePath)) {\n    destination = destination.slice(basePath.length);\n  }\n\n  return destination;\n}\n"], "names": [], "mappings": ";;;AAKO,SAAS,eAAe,EAAA,EAAoB;IACjD,IAAI,cAAc;IAClB,MAAM,WAAW,QAAQ,IAAI;IAC7B,IAAI,YAAY,YAAY,UAAA,CAAW,QAAQ,GAAG;;IAElD;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/app-router/client/useInternalNavFun.ts"], "sourcesContent": ["import type { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';\nimport { usePathname } from 'next/navigation';\nimport { useCallback, useEffect, useTransition } from 'react';\n\nimport type { NextClerkProviderProps } from '../../types';\nimport { removeBasePath } from '../../utils/removeBasePath';\n\ndeclare global {\n  interface Window {\n    __clerk_internal_navigations: Record<\n      string,\n      {\n        fun: NonNullable<NextClerkProviderProps['routerPush'] | NextClerkProviderProps['routerReplace']>;\n        promisesBuffer: Array<() => void> | undefined;\n      }\n    >;\n  }\n}\n\nconst getClerkNavigationObject = (name: string) => {\n  window.__clerk_internal_navigations ??= {};\n  // @ts-ignore\n  window.__clerk_internal_navigations[name] ??= {};\n  return window.__clerk_internal_navigations[name];\n};\n\nexport const useInternalNavFun = (props: {\n  windowNav: typeof window.history.pushState | typeof window.history.replaceState | undefined;\n  routerNav: AppRouterInstance['push'] | AppRouterInstance['replace'];\n  name: string;\n}) => {\n  const { windowNav, routerNav, name } = props;\n  const pathname = usePathname();\n  const [isPending, startTransition] = useTransition();\n\n  if (windowNav) {\n    getClerkNavigationObject(name).fun = (to, opts) => {\n      return new Promise<void>(res => {\n        // We need to use window to store the reference to the buffer,\n        // as ClerkProvider might be unmounted and remounted during navigations\n        // If we use a ref, it will be reset when ClerkProvider is unmounted\n        getClerkNavigationObject(name).promisesBuffer ??= [];\n        getClerkNavigationObject(name).promisesBuffer?.push(res);\n        startTransition(() => {\n          // If the navigation is internal, we should use the history API to navigate\n          // as this is the way to perform a shallow navigation in Next.js App Router\n          // without unmounting/remounting the page or fetching data from the server.\n          if (opts?.__internal_metadata?.navigationType === 'internal') {\n            // In 14.1.0, useSearchParams becomes reactive to shallow updates,\n            // but only if passing `null` as the history state.\n            // Older versions need to maintain the history state for push/replace to work,\n            // without affecting how the Next router works.\n            const state = ((window as any).next?.version ?? '') < '14.1.0' ? history.state : null;\n            windowNav(state, '', to);\n          } else {\n            // If the navigation is external (usually when navigating away from the component but still within the app),\n            // we should use the Next.js router to navigate as it will handle updating the URL and also\n            // fetching the new page if necessary.\n            routerNav(removeBasePath(to));\n          }\n        });\n      });\n    };\n  }\n\n  const flushPromises = () => {\n    getClerkNavigationObject(name).promisesBuffer?.forEach(resolve => resolve());\n    getClerkNavigationObject(name).promisesBuffer = [];\n  };\n\n  // Flush any pending promises on mount/unmount\n  useEffect(() => {\n    flushPromises();\n    return flushPromises;\n  }, []);\n\n  // Handle flushing the promise buffer when a navigation happens\n  useEffect(() => {\n    if (!isPending) {\n      flushPromises();\n    }\n  }, [pathname, isPending]);\n\n  return useCallback((to: string) => {\n    return getClerkNavigationObject(name).fun(to);\n    // We are not expecting name to change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n};\n"], "names": ["_a", "_b", "_c"], "mappings": ";;;AACA,SAAS,mBAAmB;AAC5B,SAAS,aAAa,WAAW,qBAAqB;AAGtD,SAAS,sBAAsB;;;;AAc/B,MAAM,2BAA2B,CAAC,SAAiB;IAnBnD,IAAA,IAAA,IAAA;IAoBE,CAAA,KAAA,OAAO,4BAAA,KAAP,OAAA,KAAA,OAAO,4BAAA,GAAiC,CAAC;IAEzC,CAAA,KAAA,CAAA,KAAA,OAAO,4BAAA,CAAA,CAAP,KAAA,KAAA,OAAA,KAAA,EAAA,CAAA,KAAA,GAA8C,CAAC;IAC/C,OAAO,OAAO,4BAAA,CAA6B,IAAI,CAAA;AACjD;AAEO,MAAM,oBAAoB,CAAC,UAI5B;IACJ,MAAM,EAAE,SAAA,EAAW,SAAA,EAAW,IAAA,CAAK,CAAA,GAAI;IACvC,MAAM,eAAW,iJAAA,CAAY;IAC7B,MAAM,CAAC,WAAW,eAAe,CAAA,6MAAI,gBAAA,CAAc;IAEnD,IAAI,WAAW;QACb,yBAAyB,IAAI,EAAE,GAAA,GAAM,CAAC,IAAI,SAAS;YACjD,OAAO,IAAI,QAAc,CAAA,QAAO;gBArCtC,IAAA,IAAA,IAAA;gBAyCQ,CAAA,KAAA,CAAA,KAAA,yBAAyB,IAAI,CAAA,EAAE,cAAA,KAA/B,OAAA,KAAA,GAA+B,cAAA,GAAmB,CAAC,CAAA;gBACnD,CAAA,KAAA,yBAAyB,IAAI,EAAE,cAAA,KAA/B,OAAA,KAAA,IAAA,GAA+C,IAAA,CAAK;gBACpD,gBAAgB,MAAM;oBA3C9B,IAAAA,KAAAC,KAAAC;oBA+CU,IAAA,CAAA,CAAIF,MAAA,QAAA,OAAA,KAAA,IAAA,KAAM,mBAAA,KAAN,OAAA,KAAA,IAAAA,IAA2B,cAAA,MAAmB,YAAY;wBAK5D,MAAM,QAAA,CAAA,CAAUE,MAAAA,CAAAD,MAAA,OAAe,IAAA,KAAf,OAAA,KAAA,IAAAA,IAAqB,OAAA,KAArB,OAAAC,MAAgC,EAAA,IAAM,WAAW,QAAQ,KAAA,GAAQ;wBACjF,UAAU,OAAO,IAAI,EAAE;oBACzB,OAAO;wBAIL,0LAAU,iBAAA,EAAe,EAAE,CAAC;oBAC9B;gBACF,CAAC;YACH,CAAC;QACH;IACF;IAEA,MAAM,gBAAgB,MAAM;QAjE9B,IAAA;QAkEI,CAAA,KAAA,yBAAyB,IAAI,EAAE,cAAA,KAA/B,OAAA,KAAA,IAAA,GAA+C,OAAA,CAAQ,CAAA,UAAW,QAAQ;QAC1E,yBAAyB,IAAI,EAAE,cAAA,GAAiB,CAAC,CAAA;IACnD;IAGA,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QACd,cAAc;QACd,OAAO;IACT,GAAG,CAAC,CAAC;IAGL,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QACd,IAAI,CAAC,WAAW;YACd,cAAc;QAChB;IACF,GAAG;QAAC;QAAU,SAAS;KAAC;IAExB,iNAAO,cAAA,EAAY,CAAC,OAAe;QACjC,OAAO,yBAAyB,IAAI,EAAE,GAAA,CAAI,EAAE;IAG9C,GAAG,CAAC,CAAC;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/app-router/client/useAwaitablePush.ts"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\n\nimport { useInternalNavFun } from './useInternalNavFun';\n\n/**\n * Creates an \"awaitable\" navigation function that will do its best effort to wait for Next.js to finish its route transition.\n * This is accomplished by wrapping the call to `router.push` in `startTransition()`, which should rely on <PERSON>act to coordinate the pending state. We key off of\n * `isPending` to flush the stored promises and ensure the navigates \"resolve\".\n */\nexport const useAwaitablePush = () => {\n  const router = useRouter();\n\n  return useInternalNavFun({\n    windowNav: typeof window !== 'undefined' ? window.history.pushState.bind(window.history) : undefined,\n    routerNav: router.push.bind(router),\n    name: 'push',\n  });\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,iBAAiB;AAE1B,SAAS,yBAAyB;;;;AAO3B,MAAM,mBAAmB,MAAM;IACpC,MAAM,gJAAS,YAAA,CAAU;IAEzB,4MAAO,oBAAA,EAAkB;QACvB,WAAW,OAAO,WAAW,cAAc,OAAO,OAAA,CAAQ,SAAA,CAAU,IAAA,CAAK,OAAO,OAAO,IAAI,KAAA;QAC3F,WAAW,OAAO,IAAA,CAAK,IAAA,CAAK,MAAM;QAClC,MAAM;IACR,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/app-router/client/useAwaitableReplace.ts"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\n\nimport { useInternalNavFun } from './useInternalNavFun';\n\n/**\n * Creates an \"awaitable\" navigation function that will do its best effort to wait for Next.js to finish its route transition.\n * This is accomplished by wrapping the call to `router.replace` in `startTransition()`, which should rely on <PERSON>act to coordinate the pending state. We key off of\n * `isPending` to flush the stored promises and ensure the navigates \"resolve\".\n */\nexport const useAwaitableReplace = () => {\n  const router = useRouter();\n\n  return useInternalNavFun({\n    windowNav: typeof window !== 'undefined' ? window.history.replaceState.bind(window.history) : undefined,\n    routerNav: router.replace.bind(router),\n    name: 'replace',\n  });\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,iBAAiB;AAE1B,SAAS,yBAAyB;;;;AAO3B,MAAM,sBAAsB,MAAM;IACvC,MAAM,gJAAS,YAAA,CAAU;IAEzB,4MAAO,oBAAA,EAAkB;QACvB,WAAW,OAAO,WAAW,cAAc,OAAO,OAAA,CAAQ,YAAA,CAAa,IAAA,CAAK,OAAO,OAAO,IAAI,KAAA;QAC9F,WAAW,OAAO,OAAA,CAAQ,IAAA,CAAK,MAAM;QACrC,MAAM;IACR,CAAC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 664, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/app-router/client/ClerkProvider.tsx"], "sourcesContent": ["'use client';\nimport { <PERSON><PERSON><PERSON><PERSON> as ReactClerkProvider } from '@clerk/clerk-react';\nimport { inBrowser } from '@clerk/shared/browser';\nimport { logger } from '@clerk/shared/logger';\nimport { useRouter } from 'next/navigation';\nimport nextPackage from 'next/package.json';\nimport React, { useEffect, useTransition } from 'react';\n\nimport { useSafeLayoutEffect } from '../../client-boundary/hooks/useSafeLayoutEffect';\nimport { ClerkNextOptionsProvider, useClerkNextOptions } from '../../client-boundary/NextOptionsContext';\nimport type { NextClerkProviderProps } from '../../types';\nimport { ClerkJSScript } from '../../utils/clerk-js-script';\nimport { mergeNextClerkPropsWithEnv } from '../../utils/mergeNextClerkPropsWithEnv';\nimport { invalidateCacheAction } from '../server-actions';\nimport { useAwaitablePush } from './useAwaitablePush';\nimport { useAwaitableReplace } from './useAwaitableReplace';\n\ndeclare global {\n  export interface Window {\n    __clerk_nav_await: Array<(value: void) => void>;\n    __clerk_nav: (to: string) => Promise<void>;\n    __clerk_internal_invalidateCachePromise: () => void | undefined;\n    next?: {\n      version: string;\n    };\n  }\n}\n\nconst isDeprecatedNextjsVersion = nextPackage.version.startsWith('13.') || nextPackage.version.startsWith('14.0');\n\nexport const ClientClerkProvider = (props: NextClerkProviderProps) => {\n  if (isDeprecatedNextjsVersion) {\n    const deprecationWarning = `Clerk:\\nYour current Next.js version (${nextPackage.version}) will be deprecated in the next major release of \"@clerk/nextjs\". Please upgrade to next@14.1.0 or later.`;\n    if (inBrowser()) {\n      logger.warnOnce(deprecationWarning);\n    } else {\n      logger.logOnce(`\\n\\x1b[43m----------\\n${deprecationWarning}\\n----------\\x1b[0m\\n`);\n    }\n  }\n\n  const { __unstable_invokeMiddlewareOnAuthStateChange = true, children } = props;\n  const router = useRouter();\n  const push = useAwaitablePush();\n  const replace = useAwaitableReplace();\n  const [isPending, startTransition] = useTransition();\n\n  // Avoid rendering nested ClerkProviders by checking for the existence of the ClerkNextOptions context provider\n  const isNested = Boolean(useClerkNextOptions());\n  if (isNested) {\n    return props.children;\n  }\n\n  useEffect(() => {\n    if (!isPending) {\n      window.__clerk_internal_invalidateCachePromise?.();\n    }\n  }, [isPending]);\n\n  useSafeLayoutEffect(() => {\n    window.__unstable__onBeforeSetActive = () => {\n      /**\n       * We need to invalidate the cache in case the user is navigating to a page that\n       * was previously cached using the auth state that was active at the time.\n       *\n       *  We also need to await for the invalidation to happen before we navigate,\n       * otherwise the navigation will use the cached page.\n       *\n       * For example, if we did not invalidate the flow, the following scenario would be broken:\n       * - The middleware is configured in such a way that it redirects you back to the same page if a certain condition is true (eg, you need to pick an org)\n       * - The user has a <Link href=/> component in the page\n       * - The UB is mounted with afterSignOutUrl=/\n       * - The user clicks the Link. A nav to / happens, a 307 to the current page is returned so a navigation does not take place. The / navigation is now cached as a 307 to the current page\n       * - The user clicks sign out\n       * - We call router.refresh()\n       * - We navigate to / but its cached and instead, we 'redirect' to the current page\n       *\n       *  For more information on cache invalidation, see:\n       * https://nextjs.org/docs/app/building-your-application/caching#invalidation-1\n       */\n      return new Promise(res => {\n        window.__clerk_internal_invalidateCachePromise = res;\n\n        // NOTE: the following code will allow `useReverification()` to work properly when `handlerReverification` is called inside `startTransition`\n        if (window.next?.version && typeof window.next.version === 'string' && window.next.version.startsWith('13')) {\n          startTransition(() => {\n            router.refresh();\n          });\n        } else {\n          void invalidateCacheAction().then(() => res());\n        }\n      });\n    };\n\n    window.__unstable__onAfterSetActive = () => {\n      if (__unstable_invokeMiddlewareOnAuthStateChange) {\n        return router.refresh();\n      }\n    };\n  }, []);\n\n  const mergedProps = mergeNextClerkPropsWithEnv({\n    ...props,\n    routerPush: push,\n    routerReplace: replace,\n  });\n\n  return (\n    <ClerkNextOptionsProvider options={mergedProps}>\n      <ReactClerkProvider {...mergedProps}>\n        <ClerkJSScript router='app' />\n        {children}\n      </ReactClerkProvider>\n    </ClerkNextOptionsProvider>\n  );\n};\n"], "names": [], "mappings": ";;;AACA,SAAS,iBAAiB,0BAA0B;;AACpD,SAAS,iBAAiB;;;AAC1B,SAAS,cAAc;AACvB,SAAS,iBAAiB;AAC1B,OAAO,iBAAiB;AACxB,OAAO,SAAS,WAAW,qBAAqB;AAEhD,SAAS,2BAA2B;AACpC,SAAS,0BAA0B,2BAA2B;AAE9D,SAAS,qBAAqB;AAC9B,SAAS,kCAAkC;AAC3C,SAAS,6BAA6B;AACtC,SAAS,wBAAwB;AACjC,SAAS,2BAA2B;;;;;;;;;;;;;;;AAapC,MAAM,oIAA4B,UAAA,CAAY,OAAA,CAAQ,UAAA,CAAW,KAAK,6GAAK,UAAA,CAAY,OAAA,CAAQ,UAAA,CAAW,MAAM;AAEzG,MAAM,sBAAsB,CAAC,UAAkC;IACpE,IAAI,2BAA2B;QAC7B,MAAM,qBAAqB,CAAA;8BAAA,0GAAyC,UAAA,CAAY,OAAO,CAAA,0GAAA,CAAA;QACvF,wKAAI,YAAA,CAAU,IAAG;YACf,+JAAA,CAAA,SAAA,CAAO,QAAA,CAAS,kBAAkB;QACpC,OAAO;YACL,+JAAA,CAAA,SAAA,CAAO,OAAA,CAAQ,CAAA;;AAAA,EAAyB,kBAAkB,CAAA;;AAAA,CAAuB;QACnF;IACF;IAEA,MAAM,EAAE,+CAA+C,IAAA,EAAM,QAAA,CAAS,CAAA,GAAI;IAC1E,MAAM,SAAS,mJAAA,CAAU;IACzB,MAAM,2MAAO,mBAAA,CAAiB;IAC9B,MAAM,cAAU,yNAAA,CAAoB;IACpC,MAAM,CAAC,WAAW,eAAe,CAAA,6MAAI,gBAAA,CAAc;IAGnD,MAAM,WAAW,yMAAQ,sBAAA,CAAoB,CAAC;IAC9C,IAAI,UAAU;QACZ,OAAO,MAAM,QAAA;IACf;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QApDlB,IAAA;QAqDI,IAAI,CAAC,WAAW;YACd,CAAA,KAAA,OAAO,uCAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA;QACF;IACF,GAAG;QAAC,SAAS;KAAC;IAEd,CAAA,GAAA,sMAAA,CAAA,sBAAA,EAAoB,MAAM;QACxB,OAAO,6BAAA,GAAgC,MAAM;YAoB3C,OAAO,IAAI,QAAQ,CAAA,QAAO;gBA/EhC,IAAA;gBAgFQ,OAAO,uCAAA,GAA0C;gBAGjD,IAAA,CAAA,CAAI,KAAA,OAAO,IAAA,KAAP,OAAA,KAAA,IAAA,GAAa,OAAA,KAAW,OAAO,OAAO,IAAA,CAAK,OAAA,KAAY,YAAY,OAAO,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAW,IAAI,GAAG;oBAC3G,gBAAgB,MAAM;wBACpB,OAAO,OAAA,CAAQ;oBACjB,CAAC;gBACH,OAAO;oBACL,gMAAK,wBAAA,CAAsB,GAAE,IAAA,CAAK,IAAM,IAAI,CAAC;gBAC/C;YACF,CAAC;QACH;QAEA,OAAO,4BAAA,GAA+B,MAAM;YAC1C,IAAI,8CAA8C;gBAChD,OAAO,OAAO,OAAA,CAAQ;YACxB;QACF;IACF,GAAG,CAAC,CAAC;IAEL,MAAM,0MAAc,6BAAA,EAA2B;QAC7C,GAAG,KAAA;QACH,YAAY;QACZ,eAAe;IACjB,CAAC;IAED,OACE,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,8LAAC,2BAAA,EAAA;QAAyB,SAAS;IAAA,GACjC,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,6KAAC,gBAAA,EAAA;QAAoB,GAAG,WAAA;IAAA,GACtB,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,oLAAC,gBAAA,EAAA;QAAc,QAAO;IAAA,CAAM,GAC3B,QACH,CACF;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/client-boundary/hooks/usePagesRouter.tsx"], "sourcesContent": ["import { useRouter } from 'next/compat/router';\n\nexport const usePagesRouter = () => {\n  // The compat version of useRouter returns null instead of throwing an error\n  // when used inside app router instead of pages router\n  // we use it to detect if the component is used inside pages or app router\n  // so we can use the correct algorithm to get the path\n  return { pagesRouter: useRouter() };\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,iBAAiB;;AAEnB,MAAM,iBAAiB,MAAM;IAKlC,OAAO;QAAE,0JAAa,YAAA,CAAU;IAAE;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useEnforceCatchAllRoute.tsx"], "sourcesContent": ["import { isProductionEnvironment } from '@clerk/shared/utils';\nimport type { RoutingStrategy } from '@clerk/types';\nimport React from 'react';\n\nimport { useSession } from '../hooks';\nimport { usePagesRouter } from './usePagesRouter';\n\n/**\n * This ugly hook  enforces that the Clerk components are mounted in a catch-all route\n * For pages router, we can parse the pathname we get from the useRouter hook\n * For app router, there is no reliable way to do the same check right now, so we\n * fire a request to a path under window.location.href and we check whether the path\n * exists or not\n */\nexport const useEnforceCatchAllRoute = (\n  component: string,\n  path: string,\n  routing?: RoutingStrategy,\n  requireSessionBeforeCheck = true,\n) => {\n  const ref = React.useRef(0);\n  const { pagesRouter } = usePagesRouter();\n  const { session, isLoaded } = useSession();\n\n  // This check does not break the rules of hooks\n  // as the condition will remain the same for the whole app lifecycle\n  if (isProductionEnvironment()) {\n    return;\n  }\n\n  React.useEffect(() => {\n    if (!isLoaded || (routing && routing !== 'path')) {\n      return;\n    }\n\n    // For components that require an active session, like UserProfile\n    // we should not enforce the catch-all route if there is no session\n    // because these components are usually protected by the middleware\n    // and if the check runs before the session is available, it will fail\n    // even if the route is a catch-all route, as the check request will result\n    // in a 404 because of auth.protect();\n    if (requireSessionBeforeCheck && !session) {\n      return;\n    }\n\n    const ac = new AbortController();\n    const error = () => {\n      const correctPath = pagesRouter ? `${path}/[[...index]].tsx` : `${path}/[[...rest]]/page.tsx`;\n      throw new Error(\n        `\nClerk: The <${component}/> component is not configured correctly. The most likely reasons for this error are:\n\n1. The \"${path}\" route is not a catch-all route.\nIt is recommended to convert this route to a catch-all route, eg: \"${correctPath}\". Alternatively, you can update the <${component}/> component to use hash-based routing by setting the \"routing\" prop to \"hash\".\n\n2. The <${component}/> component is mounted in a catch-all route, but all routes under \"${path}\" are protected by the middleware.\nTo resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the \"createRouteMatcher\" helper, consider adding \"(.*)\" to the end of the route pattern, eg: \"${path}(.*)\". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher\n`,\n      );\n    };\n\n    if (pagesRouter) {\n      if (!pagesRouter.pathname.match(/\\[\\[\\.\\.\\..+]]/)) {\n        error();\n      }\n    } else {\n      const check = async () => {\n        // make sure to run this as soon as possible\n        // but don't run again when strict mode is enabled\n        ref.current++;\n        if (ref.current > 1) {\n          return;\n        }\n        let res;\n        try {\n          const url = `${window.location.origin}${\n            window.location.pathname\n          }/${component}_clerk_catchall_check_${Date.now()}`;\n          res = await fetch(url, { signal: ac.signal });\n        } catch (e) {\n          // no op\n        }\n        if (res?.status === 404) {\n          error();\n        }\n      };\n      void check();\n    }\n\n    return () => {\n      // make sure to run this as soon as possible\n      // but don't run again when strict mode is enabled\n      if (ref.current > 1) {\n        ac.abort();\n      }\n    };\n  }, [isLoaded]);\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,+BAA+B;;AAExC,OAAO,WAAW;AAElB,SAAS,kBAAkB;AAC3B,SAAS,sBAAsB;;;;;AASxB,MAAM,0BAA0B,CACrC,WACA,MACA,SACA,4BAA4B,IAAA,KACzB;IACH,MAAM,4MAAM,UAAA,CAAM,MAAA,CAAO,CAAC;IAC1B,MAAM,EAAE,WAAA,CAAY,CAAA,yMAAI,iBAAA,CAAe;IACvC,MAAM,EAAE,OAAA,EAAS,QAAA,CAAS,CAAA,OAAI,0KAAA,CAAW;IAIzC,wKAAI,0BAAA,CAAwB,IAAG;QAC7B;IACF;IAEA,qMAAA,CAAA,UAAA,CAAM,SAAA,CAAU,MAAM;QACpB,IAAI,CAAC,YAAa,WAAW,YAAY,QAAS;YAChD;QACF;QAQA,IAAI,6BAA6B,CAAC,SAAS;YACzC;QACF;QAEA,MAAM,KAAK,IAAI,gBAAgB;QAC/B,MAAM,QAAQ,MAAM;YAClB,MAAM,cAAc,cAAc,GAAG,IAAI,CAAA,iBAAA,CAAA,GAAsB,GAAG,IAAI,CAAA,qBAAA,CAAA;YACtE,MAAM,IAAI,MACR,CAAA;YAAA,EACM,SAAS,CAAA;;QAAA,EAEb,IAAI,CAAA;mEAAA,EACuD,WAAW,CAAA,sCAAA,EAAyC,SAAS,CAAA;;QAAA,EAExH,SAAS,CAAA,oEAAA,EAAuE,IAAI,CAAA;wNAAA,EAC4H,IAAI,CAAA;AAAA,CAAA;QAG1N;QAEA,IAAI,aAAa;YACf,IAAI,CAAC,YAAY,QAAA,CAAS,KAAA,CAAM,gBAAgB,GAAG;gBACjD,MAAM;YACR;QACF,OAAO;YACL,MAAM,QAAQ,YAAY;gBAGxB,IAAI,OAAA;gBACJ,IAAI,IAAI,OAAA,GAAU,GAAG;oBACnB;gBACF;gBACA,IAAI;gBACJ,IAAI;oBACF,MAAM,MAAM,GAAG,OAAO,QAAA,CAAS,MAAM,GACnC,OAAO,QAAA,CAAS,QAClB,CAAA,CAAA,EAAI,SAAS,CAAA,sBAAA,EAAyB,KAAK,GAAA,CAAI,CAAC,EAAA;oBAChD,MAAM,MAAM,MAAM,KAAK;wBAAE,QAAQ,GAAG,MAAA;oBAAO,CAAC;gBAC9C,EAAA,OAAS,GAAG,CAEZ;gBACA,IAAA,CAAI,OAAA,OAAA,KAAA,IAAA,IAAK,MAAA,MAAW,KAAK;oBACvB,MAAM;gBACR;YACF;YACA,KAAK,MAAM;QACb;QAEA,OAAO,MAAM;YAGX,IAAI,IAAI,OAAA,GAAU,GAAG;gBACnB,GAAG,KAAA,CAAM;YACX;QACF;IACF,GAAG;QAAC,QAAQ;KAAC;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 867, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/client-boundary/hooks/usePathnameWithoutCatchAll.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { usePagesRouter } from './usePagesRouter';\n\nexport const usePathnameWithoutCatchAll = () => {\n  const pathRef = React.useRef<string>();\n\n  const { pagesRouter } = usePagesRouter();\n\n  if (pagesRouter) {\n    if (pathRef.current) {\n      return pathRef.current;\n    } else {\n      // in pages router things are simpler as the pathname includes the catch all route\n      // which starts with [[... and we can just remove it\n      pathRef.current = pagesRouter.pathname.replace(/\\/\\[\\[\\.\\.\\..*/, '');\n      return pathRef.current;\n    }\n  }\n\n  // require is used to avoid importing next/navigation when the pages router is used,\n  // as it will throw an error. We cannot use dynamic import as it is async\n  // and we need the hook to be sync\n  // eslint-disable-next-line @typescript-eslint/no-var-requires\n  const usePathname = require('next/navigation').usePathname;\n  // eslint-disable-next-line @typescript-eslint/no-var-requires\n  const useParams = require('next/navigation').useParams;\n\n  // Get the pathname that includes any named or catch all params\n  // eg:\n  // the filesystem route /user/[id]/profile/[[...rest]]/page.tsx\n  // could give us the following pathname /user/123/profile/security\n  // if the user navigates to the security section of the user profile\n  const pathname = usePathname() || '';\n  const pathParts = pathname.split('/').filter(Boolean);\n  // the useParams hook returns an object with all named and catch all params\n  // for named params, the key in the returned object always contains a single value\n  // for catch all params, the key in the returned object contains an array of values\n  // we find the catch all params by checking if the value is an array\n  // and then we remove one path part for each catch all param\n  const catchAllParams = Object.values(useParams() || {})\n    .filter(v => Array.isArray(v))\n    .flat(Infinity);\n  // so we end up with the pathname where the components are mounted at\n  // eg /user/123/profile/security will return /user/123/profile as the path\n  if (pathRef.current) {\n    return pathRef.current;\n  } else {\n    pathRef.current = `/${pathParts.slice(0, pathParts.length - catchAllParams.length).join('/')}`;\n    return pathRef.current;\n  }\n};\n"], "names": [], "mappings": ";;;AAAA,OAAO,WAAW;AAElB,SAAS,sBAAsB;;;AAExB,MAAM,6BAA6B,MAAM;IAC9C,MAAM,gNAAU,UAAA,CAAM,MAAA,CAAe;IAErC,MAAM,EAAE,WAAA,CAAY,CAAA,OAAI,mNAAA,CAAe;IAEvC,IAAI,aAAa;QACf,IAAI,QAAQ,OAAA,EAAS;YACnB,OAAO,QAAQ,OAAA;QACjB,OAAO;YAGL,QAAQ,OAAA,GAAU,YAAY,QAAA,CAAS,OAAA,CAAQ,kBAAkB,EAAE;YACnE,OAAO,QAAQ,OAAA;QACjB;IACF;IAMA,MAAM,cAAc,QAAQ,iBAAiB,mEAAE,WAAA;IAE/C,MAAM,YAAY,QAAQ,iBAAiB,mEAAE,SAAA;IAO7C,MAAM,WAAW,YAAY,KAAK;IAClC,MAAM,YAAY,SAAS,KAAA,CAAM,GAAG,EAAE,MAAA,CAAO,OAAO;IAMpD,MAAM,iBAAiB,OAAO,MAAA,CAAO,UAAU,KAAK,CAAC,CAAC,EACnD,MAAA,CAAO,CAAA,IAAK,MAAM,OAAA,CAAQ,CAAC,CAAC,EAC5B,IAAA,CAAK,QAAQ;IAGhB,IAAI,QAAQ,OAAA,EAAS;QACnB,OAAO,QAAQ,OAAA;IACjB,OAAO;QACL,QAAQ,OAAA,GAAU,CAAA,CAAA,EAAI,UAAU,KAAA,CAAM,GAAG,UAAU,MAAA,GAAS,eAAe,MAAM,EAAE,IAAA,CAAK,GAAG,CAAC,EAAA;QAC5F,OAAO,QAAQ,OAAA;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/client-boundary/hooks/useEnforceRoutingProps.tsx"], "sourcesContent": ["import { useRoutingProps } from '@clerk/clerk-react/internal';\nimport type { RoutingOptions } from '@clerk/types';\n\nimport { useEnforceCatchAllRoute } from './useEnforceCatchAllRoute';\nimport { usePathnameWithoutCatchAll } from './usePathnameWithoutCatchAll';\n\nexport function useEnforceCorrectRoutingProps<T extends RoutingOptions>(\n  componentName: string,\n  props: T,\n  requireSessionBeforeCheck = true,\n): T {\n  const path = usePathnameWithoutCatchAll();\n  const routingProps = useRoutingProps(componentName, props, { path });\n  useEnforceCatchAllRoute(componentName, path, routingProps.routing, requireSessionBeforeCheck);\n  return routingProps;\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,uBAAuB;;AAGhC,SAAS,+BAA+B;AACxC,SAAS,kCAAkC;;;;AAEpC,SAAS,8BACd,aAAA,EACA,KAAA,EACA,4BAA4B,IAAA,EACzB;IACH,MAAM,yNAAO,6BAAA,CAA2B;IACxC,MAAM,kMAAe,kBAAA,EAAgB,eAAe,OAAO;QAAE;IAAK,CAAC;IACnE,CAAA,GAAA,0MAAA,CAAA,0BAAA,EAAwB,eAAe,MAAM,aAAa,OAAA,EAAS,yBAAyB;IAC5F,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40clerk/nextjs/src/client-boundary/uiComponents.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  OrganizationProfile as BaseOrganizationProfile,\n  SignIn as BaseSignIn,\n  SignUp as BaseSignUp,\n  UserProfile as BaseUserProfile,\n} from '@clerk/clerk-react';\nimport type { OrganizationProfileProps, SignInProps, SignUpProps, UserProfileProps } from '@clerk/types';\nimport React from 'react';\n\nimport { useEnforceCorrectRoutingProps } from './hooks/useEnforceRoutingProps';\n\nexport {\n  CreateOrganization,\n  OrganizationList,\n  OrganizationSwitcher,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUpButton,\n  UserButton,\n  GoogleOneTap,\n  Waitlist,\n} from '@clerk/clerk-react';\n\n// The assignment of UserProfile with BaseUserProfile props is used\n// to support the CustomPage functionality (eg UserProfile.Page)\n// Also the `typeof BaseUserProfile` is used to resolve the following error:\n// \"The inferred type of 'UserProfile' cannot be named without a reference to ...\"\nexport const UserProfile: typeof BaseUserProfile = Object.assign(\n  (props: UserProfileProps) => {\n    return <BaseUserProfile {...useEnforceCorrectRoutingProps('UserProfile', props)} />;\n  },\n  { ...BaseUserProfile },\n);\n\n// The assignment of OrganizationProfile with BaseOrganizationProfile props is used\n// to support the CustomPage functionality (eg OrganizationProfile.Page)\n// Also the `typeof BaseOrganizationProfile` is used to resolved the following error:\n// \"The inferred type of 'OrganizationProfile' cannot be named without a reference to ...\"\nexport const OrganizationProfile: typeof BaseOrganizationProfile = Object.assign(\n  (props: OrganizationProfileProps) => {\n    return <BaseOrganizationProfile {...useEnforceCorrectRoutingProps('OrganizationProfile', props)} />;\n  },\n  { ...BaseOrganizationProfile },\n);\n\nexport const SignIn = (props: SignInProps) => {\n  return <BaseSignIn {...useEnforceCorrectRoutingProps('SignIn', props, false)} />;\n};\n\nexport const SignUp = (props: SignUpProps) => {\n  return <BaseSignUp {...useEnforceCorrectRoutingProps('SignUp', props, false)} />;\n};\n"], "names": [], "mappings": ";;;;;;AAEA;AAOA,OAAO,WAAW;AAElB,SAAS,qCAAqC;;;;;;AAmBvC,MAAM,cAAsC,OAAO,MAAA,CACxD,CAAC,UAA4B;IAC3B,OAAO,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,CAAC,0LAAA,EAAA;QAAiB,iNAAG,gCAAA,EAA8B,eAAe,KAAK,CAAA;IAAA,CAAG;AACnF,GACA;IAAE,+KAAG,cAAA;AAAgB;AAOhB,MAAM,sBAAsD,OAAO,MAAA,CACxE,CAAC,UAAoC;IACnC,OAAO,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,4KAAC,uBAAA,EAAA;QAAyB,iNAAG,gCAAA,EAA8B,uBAAuB,KAAK,CAAA;IAAA,CAAG;AACnG,GACA;IAAE,+KAAG,sBAAA;AAAwB;AAGxB,MAAM,SAAS,CAAC,UAAuB;IAC5C,OAAO,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,6KAAC,SAAA,EAAA;QAAY,iNAAG,gCAAA,EAA8B,UAAU,OAAO,KAAK,CAAA;IAAA,CAAG;AAChF;AAEO,MAAM,SAAS,CAAC,UAAuB;IAC5C,OAAO,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,6KAAC,SAAA,EAAA;QAAY,iNAAG,gCAAA,EAA8B,UAAU,OAAO,KAAK,CAAA;IAAA,CAAG;AAChF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1083, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40swc/helpers/cjs/_class_private_field_loose_base.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _class_private_field_loose_base(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n        throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n\n    return receiver;\n}\nexports._ = _class_private_field_loose_base;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,gCAAgC,QAAQ,EAAE,UAAU;IACzD,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,aAAa;QAC7D,MAAM,IAAI,UAAU;IACxB;IAEA,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40swc/helpers/cjs/_class_private_field_loose_key.cjs"], "sourcesContent": ["\"use strict\";\n\nvar id = 0;\n\nfunction _class_private_field_loose_key(name) {\n    return \"__private_\" + id++ + \"_\" + name;\n}\nexports._ = _class_private_field_loose_key;\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,KAAK;AAET,SAAS,+BAA+B,IAAI;IACxC,OAAO,eAAe,OAAO,MAAM;AACvC;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/%40swc/helpers/cjs/_tagged_template_literal_loose.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _tagged_template_literal_loose(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    strings.raw = raw;\n\n    return strings;\n}\nexports._ = _tagged_template_literal_loose;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,+BAA+B,OAAO,EAAE,GAAG;IAChD,IAAI,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC;IAE9B,QAAQ,GAAG,GAAG;IAEd,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/sonner/src/index.tsx", "file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/sonner/src/assets.tsx", "file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/sonner/src/hooks.tsx", "file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/sonner/src/state.ts", "file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/sonner/dist/%23style-inject%3A%23style-inject", "file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/sonner/src/styles.css", "file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/node_modules/sonner/src/types.ts"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\nimport ReactD<PERSON> from 'react-dom';\n\nimport { CloseIcon, getAsset, Loader } from './assets';\nimport { useIsDocumentHidden } from './hooks';\nimport { toast, ToastState } from './state';\nimport './styles.css';\nimport {\n  isAction,\n  type ExternalToast,\n  type HeightT,\n  type ToasterProps,\n  type ToastProps,\n  type ToastT,\n  type ToastToDismiss,\n} from './types';\n\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n\n// Viewport padding\nconst VIEWPORT_OFFSET = '32px';\n\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n\n// Default toast width\nconst TOAST_WIDTH = 356;\n\n// Default gap between toasts\nconst GAP = 14;\n\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 20;\n\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\n\nfunction _cn(...classes: (string | undefined)[]) {\n  return classes.filter(Boolean).join(' ');\n}\n\nconst Toast = (props: ToastProps) => {\n  const {\n    invert: ToasterInvert,\n    toast,\n    unstyled,\n    interacting,\n    setHeights,\n    visibleToasts,\n    heights,\n    index,\n    toasts,\n    expanded,\n    removeToast,\n    defaultRichColors,\n    closeButton: closeButtonFromToaster,\n    style,\n    cancelButtonStyle,\n    actionButtonStyle,\n    className = '',\n    descriptionClassName = '',\n    duration: durationFromToaster,\n    position,\n    gap,\n    loadingIcon: loadingIconProp,\n    expandByDefault,\n    classNames,\n    icons,\n    closeButtonAriaLabel = 'Close toast',\n    pauseWhenPageIsHidden,\n    cn,\n  } = props;\n  const [mounted, setMounted] = React.useState(false);\n  const [removed, setRemoved] = React.useState(false);\n  const [swiping, setSwiping] = React.useState(false);\n  const [swipeOut, setSwipeOut] = React.useState(false);\n  const [isSwiped, setIsSwiped] = React.useState(false);\n  const [offsetBeforeRemove, setOffsetBeforeRemove] = React.useState(0);\n  const [initialHeight, setInitialHeight] = React.useState(0);\n  const remainingTime = React.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n  const dragStartTime = React.useRef<Date | null>(null);\n  const toastRef = React.useRef<HTMLLIElement>(null);\n  const isFront = index === 0;\n  const isVisible = index + 1 <= visibleToasts;\n  const toastType = toast.type;\n  const dismissible = toast.dismissible !== false;\n  const toastClassname = toast.className || '';\n  const toastDescriptionClassname = toast.descriptionClassName || '';\n  // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n  const heightIndex = React.useMemo(\n    () => heights.findIndex((height) => height.toastId === toast.id) || 0,\n    [heights, toast.id],\n  );\n  const closeButton = React.useMemo(\n    () => toast.closeButton ?? closeButtonFromToaster,\n    [toast.closeButton, closeButtonFromToaster],\n  );\n  const duration = React.useMemo(\n    () => toast.duration || durationFromToaster || TOAST_LIFETIME,\n    [toast.duration, durationFromToaster],\n  );\n  const closeTimerStartTimeRef = React.useRef(0);\n  const offset = React.useRef(0);\n  const lastCloseTimerStartTimeRef = React.useRef(0);\n  const pointerStartRef = React.useRef<{ x: number; y: number } | null>(null);\n  const [y, x] = position.split('-');\n  const toastsHeightBefore = React.useMemo(() => {\n    return heights.reduce((prev, curr, reducerIndex) => {\n      // Calculate offset up until current  toast\n      if (reducerIndex >= heightIndex) {\n        return prev;\n      }\n\n      return prev + curr.height;\n    }, 0);\n  }, [heights, heightIndex]);\n  const isDocumentHidden = useIsDocumentHidden();\n\n  const invert = toast.invert || ToasterInvert;\n  const disabled = toastType === 'loading';\n\n  offset.current = React.useMemo(() => heightIndex * gap + toastsHeightBefore, [heightIndex, toastsHeightBefore]);\n\n  React.useEffect(() => {\n    // Trigger enter animation without using CSS animation\n    setMounted(true);\n  }, []);\n\n  React.useEffect(() => {\n    const toastNode = toastRef.current;\n    if (toastNode) {\n      const height = toastNode.getBoundingClientRect().height;\n      // Add toast height to heights array after the toast is mounted\n      setInitialHeight(height);\n      setHeights((h) => [{ toastId: toast.id, height, position: toast.position }, ...h]);\n      return () => setHeights((h) => h.filter((height) => height.toastId !== toast.id));\n    }\n  }, [setHeights, toast.id]);\n\n  React.useLayoutEffect(() => {\n    if (!mounted) return;\n    const toastNode = toastRef.current;\n    const originalHeight = toastNode.style.height;\n    toastNode.style.height = 'auto';\n    const newHeight = toastNode.getBoundingClientRect().height;\n    toastNode.style.height = originalHeight;\n\n    setInitialHeight(newHeight);\n\n    setHeights((heights) => {\n      const alreadyExists = heights.find((height) => height.toastId === toast.id);\n      if (!alreadyExists) {\n        return [{ toastId: toast.id, height: newHeight, position: toast.position }, ...heights];\n      } else {\n        return heights.map((height) => (height.toastId === toast.id ? { ...height, height: newHeight } : height));\n      }\n    });\n  }, [mounted, toast.title, toast.description, setHeights, toast.id]);\n\n  const deleteToast = React.useCallback(() => {\n    // Save the offset for the exit swipe animation\n    setRemoved(true);\n    setOffsetBeforeRemove(offset.current);\n    setHeights((h) => h.filter((height) => height.toastId !== toast.id));\n\n    setTimeout(() => {\n      removeToast(toast);\n    }, TIME_BEFORE_UNMOUNT);\n  }, [toast, removeToast, setHeights, offset]);\n\n  React.useEffect(() => {\n    if ((toast.promise && toastType === 'loading') || toast.duration === Infinity || toast.type === 'loading') return;\n    let timeoutId: NodeJS.Timeout;\n\n    // Pause the timer on each hover\n    const pauseTimer = () => {\n      if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n        // Get the elapsed time since the timer started\n        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n\n        remainingTime.current = remainingTime.current - elapsedTime;\n      }\n\n      lastCloseTimerStartTimeRef.current = new Date().getTime();\n    };\n\n    const startTimer = () => {\n      // setTimeout(, Infinity) behaves as if the delay is 0.\n      // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n      // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n      if (remainingTime.current === Infinity) return;\n\n      closeTimerStartTimeRef.current = new Date().getTime();\n\n      // Let the toast know it has started\n      timeoutId = setTimeout(() => {\n        toast.onAutoClose?.(toast);\n        deleteToast();\n      }, remainingTime.current);\n    };\n\n    if (expanded || interacting || (pauseWhenPageIsHidden && isDocumentHidden)) {\n      pauseTimer();\n    } else {\n      startTimer();\n    }\n\n    return () => clearTimeout(timeoutId);\n  }, [expanded, interacting, toast, toastType, pauseWhenPageIsHidden, isDocumentHidden, deleteToast]);\n\n  React.useEffect(() => {\n    if (toast.delete) {\n      deleteToast();\n    }\n  }, [deleteToast, toast.delete]);\n\n  function getLoadingIcon() {\n    if (icons?.loading) {\n      return (\n        <div\n          className={cn(classNames?.loader, toast?.classNames?.loader, 'sonner-loader')}\n          data-visible={toastType === 'loading'}\n        >\n          {icons.loading}\n        </div>\n      );\n    }\n\n    if (loadingIconProp) {\n      return (\n        <div\n          className={cn(classNames?.loader, toast?.classNames?.loader, 'sonner-loader')}\n          data-visible={toastType === 'loading'}\n        >\n          {loadingIconProp}\n        </div>\n      );\n    }\n    return <Loader className={cn(classNames?.loader, toast?.classNames?.loader)} visible={toastType === 'loading'} />;\n  }\n\n  return (\n    <li\n      tabIndex={0}\n      ref={toastRef}\n      className={cn(\n        className,\n        toastClassname,\n        classNames?.toast,\n        toast?.classNames?.toast,\n        classNames?.default,\n        classNames?.[toastType],\n        toast?.classNames?.[toastType],\n      )}\n      data-sonner-toast=\"\"\n      data-rich-colors={toast.richColors ?? defaultRichColors}\n      data-styled={!Boolean(toast.jsx || toast.unstyled || unstyled)}\n      data-mounted={mounted}\n      data-promise={Boolean(toast.promise)}\n      data-swiped={isSwiped}\n      data-removed={removed}\n      data-visible={isVisible}\n      data-y-position={y}\n      data-x-position={x}\n      data-index={index}\n      data-front={isFront}\n      data-swiping={swiping}\n      data-dismissible={dismissible}\n      data-type={toastType}\n      data-invert={invert}\n      data-swipe-out={swipeOut}\n      data-expanded={Boolean(expanded || (expandByDefault && mounted))}\n      style={\n        {\n          '--index': index,\n          '--toasts-before': index,\n          '--z-index': toasts.length - index,\n          '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n          '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n          ...style,\n          ...toast.style,\n        } as React.CSSProperties\n      }\n      onPointerDown={(event) => {\n        if (disabled || !dismissible) return;\n        dragStartTime.current = new Date();\n        setOffsetBeforeRemove(offset.current);\n        // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n        (event.target as HTMLElement).setPointerCapture(event.pointerId);\n        if ((event.target as HTMLElement).tagName === 'BUTTON') return;\n        setSwiping(true);\n        pointerStartRef.current = { x: event.clientX, y: event.clientY };\n      }}\n      onPointerUp={() => {\n        if (swipeOut || !dismissible) return;\n\n        pointerStartRef.current = null;\n        const swipeAmount = Number(toastRef.current?.style.getPropertyValue('--swipe-amount').replace('px', '') || 0);\n        const timeTaken = new Date().getTime() - dragStartTime.current?.getTime();\n        const velocity = Math.abs(swipeAmount) / timeTaken;\n\n        // Remove only if threshold is met\n        if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n          setOffsetBeforeRemove(offset.current);\n          toast.onDismiss?.(toast);\n          deleteToast();\n          setSwipeOut(true);\n          setIsSwiped(false);\n          return;\n        }\n\n        toastRef.current?.style.setProperty('--swipe-amount', '0px');\n        setSwiping(false);\n      }}\n      onPointerMove={(event) => {\n        if (!pointerStartRef.current || !dismissible) return;\n\n        const yPosition = event.clientY - pointerStartRef.current.y;\n        const isHighlighted = window.getSelection()?.toString().length > 0;\n        const swipeAmount = Number(toastRef.current?.style.getPropertyValue('--swipe-amount').replace('px', '') || 0);\n\n        if (swipeAmount > 0) {\n          setIsSwiped(true);\n        }\n\n        if (isHighlighted) return;\n\n        toastRef.current?.style.setProperty('--swipe-amount', `${Math.max(0, yPosition)}px`);\n      }}\n    >\n      {closeButton && !toast.jsx ? (\n        <button\n          aria-label={closeButtonAriaLabel}\n          data-disabled={disabled}\n          data-close-button\n          onClick={\n            disabled || !dismissible\n              ? () => {}\n              : () => {\n                  deleteToast();\n                  toast.onDismiss?.(toast);\n                }\n          }\n          className={cn(classNames?.closeButton, toast?.classNames?.closeButton)}\n        >\n          {icons?.close ?? CloseIcon}\n        </button>\n      ) : null}\n      {/* TODO: This can be cleaner */}\n      {toast.jsx || React.isValidElement(toast.title) ? (\n        toast.jsx ? (\n          toast.jsx\n        ) : typeof toast.title === 'function' ? (\n          toast.title()\n        ) : (\n          toast.title\n        )\n      ) : (\n        <>\n          {toastType || toast.icon || toast.promise ? (\n            <div data-icon=\"\" className={cn(classNames?.icon, toast?.classNames?.icon)}>\n              {toast.promise || (toast.type === 'loading' && !toast.icon) ? toast.icon || getLoadingIcon() : null}\n              {toast.type !== 'loading' ? toast.icon || icons?.[toastType] || getAsset(toastType) : null}\n            </div>\n          ) : null}\n\n          <div data-content=\"\" className={cn(classNames?.content, toast?.classNames?.content)}>\n            <div data-title=\"\" className={cn(classNames?.title, toast?.classNames?.title)}>\n              {typeof toast.title === 'function' ? toast.title() : toast.title}\n            </div>\n            {toast.description ? (\n              <div\n                data-description=\"\"\n                className={cn(\n                  descriptionClassName,\n                  toastDescriptionClassname,\n                  classNames?.description,\n                  toast?.classNames?.description,\n                )}\n              >\n                {typeof toast.description === 'function' ? toast.description() : toast.description}\n              </div>\n            ) : null}\n          </div>\n          {React.isValidElement(toast.cancel) ? (\n            toast.cancel\n          ) : toast.cancel && isAction(toast.cancel) ? (\n            <button\n              data-button\n              data-cancel\n              style={toast.cancelButtonStyle || cancelButtonStyle}\n              onClick={(event) => {\n                // We need to check twice because typescript\n                if (!isAction(toast.cancel)) return;\n                if (!dismissible) return;\n                toast.cancel.onClick?.(event);\n                deleteToast();\n              }}\n              className={cn(classNames?.cancelButton, toast?.classNames?.cancelButton)}\n            >\n              {toast.cancel.label}\n            </button>\n          ) : null}\n          {React.isValidElement(toast.action) ? (\n            toast.action\n          ) : toast.action && isAction(toast.action) ? (\n            <button\n              data-button\n              data-action\n              style={toast.actionButtonStyle || actionButtonStyle}\n              onClick={(event) => {\n                // We need to check twice because typescript\n                if (!isAction(toast.action)) return;\n                toast.action.onClick?.(event);\n                if (event.defaultPrevented) return;\n                deleteToast();\n              }}\n              className={cn(classNames?.actionButton, toast?.classNames?.actionButton)}\n            >\n              {toast.action.label}\n            </button>\n          ) : null}\n        </>\n      )}\n    </li>\n  );\n};\n\nfunction getDocumentDirection(): ToasterProps['dir'] {\n  if (typeof window === 'undefined') return 'ltr';\n  if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n\n  const dirAttribute = document.documentElement.getAttribute('dir');\n\n  if (dirAttribute === 'auto' || !dirAttribute) {\n    return window.getComputedStyle(document.documentElement).direction as ToasterProps['dir'];\n  }\n\n  return dirAttribute as ToasterProps['dir'];\n}\n\nfunction useSonner() {\n  const [activeToasts, setActiveToasts] = React.useState<ToastT[]>([]);\n\n  React.useEffect(() => {\n    return ToastState.subscribe((toast) => {\n      setActiveToasts((currentToasts) => {\n        if ('dismiss' in toast && toast.dismiss) {\n          return currentToasts.filter((t) => t.id !== toast.id);\n        }\n\n        const existingToastIndex = currentToasts.findIndex((t) => t.id === toast.id);\n        if (existingToastIndex !== -1) {\n          const updatedToasts = [...currentToasts];\n          updatedToasts[existingToastIndex] = { ...updatedToasts[existingToastIndex], ...toast };\n          return updatedToasts;\n        } else {\n          return [toast, ...currentToasts];\n        }\n      });\n    });\n  }, []);\n\n  return {\n    toasts: activeToasts,\n  };\n}\n\nconst Toaster = forwardRef<HTMLElement, ToasterProps>(function Toaster(props, ref) {\n  const {\n    invert,\n    position = 'bottom-right',\n    hotkey = ['altKey', 'KeyT'],\n    expand,\n    closeButton,\n    className,\n    offset,\n    theme = 'light',\n    richColors,\n    duration,\n    style,\n    visibleToasts = VISIBLE_TOASTS_AMOUNT,\n    toastOptions,\n    dir = getDocumentDirection(),\n    gap = GAP,\n    loadingIcon,\n    icons,\n    containerAriaLabel = 'Notifications',\n    pauseWhenPageIsHidden,\n    cn = _cn,\n  } = props;\n  const [toasts, setToasts] = React.useState<ToastT[]>([]);\n  const possiblePositions = React.useMemo(() => {\n    return Array.from(\n      new Set([position].concat(toasts.filter((toast) => toast.position).map((toast) => toast.position))),\n    );\n  }, [toasts, position]);\n  const [heights, setHeights] = React.useState<HeightT[]>([]);\n  const [expanded, setExpanded] = React.useState(false);\n  const [interacting, setInteracting] = React.useState(false);\n  const [actualTheme, setActualTheme] = React.useState(\n    theme !== 'system'\n      ? theme\n      : typeof window !== 'undefined'\n      ? window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches\n        ? 'dark'\n        : 'light'\n      : 'light',\n  );\n\n  const listRef = React.useRef<HTMLOListElement>(null);\n  const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n  const lastFocusedElementRef = React.useRef<HTMLElement>(null);\n  const isFocusWithinRef = React.useRef(false);\n\n  const removeToast = React.useCallback((toastToRemove: ToastT) => {\n    setToasts((toasts) => {\n      if (!toasts.find((toast) => toast.id === toastToRemove.id)?.delete) {\n        ToastState.dismiss(toastToRemove.id);\n      }\n\n      return toasts.filter(({ id }) => id !== toastToRemove.id);\n    });\n  }, []);\n\n  React.useEffect(() => {\n    return ToastState.subscribe((toast) => {\n      if ((toast as ToastToDismiss).dismiss) {\n        setToasts((toasts) => toasts.map((t) => (t.id === toast.id ? { ...t, delete: true } : t)));\n        return;\n      }\n\n      // Prevent batching, temp solution.\n      setTimeout(() => {\n        ReactDOM.flushSync(() => {\n          setToasts((toasts) => {\n            const indexOfExistingToast = toasts.findIndex((t) => t.id === toast.id);\n\n            // Update the toast if it already exists\n            if (indexOfExistingToast !== -1) {\n              return [\n                ...toasts.slice(0, indexOfExistingToast),\n                { ...toasts[indexOfExistingToast], ...toast },\n                ...toasts.slice(indexOfExistingToast + 1),\n              ];\n            }\n\n            return [toast, ...toasts];\n          });\n        });\n      });\n    });\n  }, []);\n\n  React.useEffect(() => {\n    if (theme !== 'system') {\n      setActualTheme(theme);\n      return;\n    }\n\n    if (theme === 'system') {\n      // check if current preference is dark\n      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n        // it's currently dark\n        setActualTheme('dark');\n      } else {\n        // it's not dark\n        setActualTheme('light');\n      }\n    }\n\n    if (typeof window === 'undefined') return;\n    const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n\n    try {\n      // Chrome & Firefox\n      darkMediaQuery.addEventListener('change', ({ matches }) => {\n        if (matches) {\n          setActualTheme('dark');\n        } else {\n          setActualTheme('light');\n        }\n      });\n    } catch (error) {\n      // Safari < 14\n      darkMediaQuery.addListener(({ matches }) => {\n        try {\n          if (matches) {\n            setActualTheme('dark');\n          } else {\n            setActualTheme('light');\n          }\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }, [theme]);\n\n  React.useEffect(() => {\n    // Ensure expanded is always false when no toasts are present / only one left\n    if (toasts.length <= 1) {\n      setExpanded(false);\n    }\n  }, [toasts]);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      const isHotkeyPressed = hotkey.every((key) => (event as any)[key] || event.code === key);\n\n      if (isHotkeyPressed) {\n        setExpanded(true);\n        listRef.current?.focus();\n      }\n\n      if (\n        event.code === 'Escape' &&\n        (document.activeElement === listRef.current || listRef.current?.contains(document.activeElement))\n      ) {\n        setExpanded(false);\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [hotkey]);\n\n  React.useEffect(() => {\n    if (listRef.current) {\n      return () => {\n        if (lastFocusedElementRef.current) {\n          lastFocusedElementRef.current.focus({ preventScroll: true });\n          lastFocusedElementRef.current = null;\n          isFocusWithinRef.current = false;\n        }\n      };\n    }\n  }, [listRef.current]);\n\n  return (\n    // Remove item from normal navigation flow, only available via hotkey\n    <section\n      aria-label={`${containerAriaLabel} ${hotkeyLabel}`}\n      tabIndex={-1}\n      aria-live=\"polite\"\n      aria-relevant=\"additions text\"\n      aria-atomic=\"false\"\n    >\n      {possiblePositions.map((position, index) => {\n        const [y, x] = position.split('-');\n\n        if (!toasts.length) return null;\n\n        return (\n          <ol\n            key={position}\n            dir={dir === 'auto' ? getDocumentDirection() : dir}\n            tabIndex={-1}\n            ref={listRef}\n            className={className}\n            data-sonner-toaster\n            data-theme={actualTheme}\n            data-y-position={y}\n            data-lifted={expanded && toasts.length > 1 && !expand}\n            data-x-position={x}\n            style={\n              {\n                '--front-toast-height': `${heights[0]?.height || 0}px`,\n                '--offset': typeof offset === 'number' ? `${offset}px` : offset || VIEWPORT_OFFSET,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n              } as React.CSSProperties\n            }\n            onBlur={(event) => {\n              if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                isFocusWithinRef.current = false;\n                if (lastFocusedElementRef.current) {\n                  lastFocusedElementRef.current.focus({ preventScroll: true });\n                  lastFocusedElementRef.current = null;\n                }\n              }\n            }}\n            onFocus={(event) => {\n              const isNotDismissible =\n                event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n\n              if (isNotDismissible) return;\n\n              if (!isFocusWithinRef.current) {\n                isFocusWithinRef.current = true;\n                lastFocusedElementRef.current = event.relatedTarget as HTMLElement;\n              }\n            }}\n            onMouseEnter={() => setExpanded(true)}\n            onMouseMove={() => setExpanded(true)}\n            onMouseLeave={() => {\n              // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n              if (!interacting) {\n                setExpanded(false);\n              }\n            }}\n            onPointerDown={(event) => {\n              const isNotDismissible =\n                event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n\n              if (isNotDismissible) return;\n              setInteracting(true);\n            }}\n            onPointerUp={() => setInteracting(false)}\n          >\n            {toasts\n              .filter((toast) => (!toast.position && index === 0) || toast.position === position)\n              .map((toast, index) => (\n                <Toast\n                  key={toast.id}\n                  icons={icons}\n                  index={index}\n                  toast={toast}\n                  defaultRichColors={richColors}\n                  duration={toastOptions?.duration ?? duration}\n                  className={toastOptions?.className}\n                  descriptionClassName={toastOptions?.descriptionClassName}\n                  invert={invert}\n                  visibleToasts={visibleToasts}\n                  closeButton={toastOptions?.closeButton ?? closeButton}\n                  interacting={interacting}\n                  position={position}\n                  style={toastOptions?.style}\n                  unstyled={toastOptions?.unstyled}\n                  classNames={toastOptions?.classNames}\n                  cancelButtonStyle={toastOptions?.cancelButtonStyle}\n                  actionButtonStyle={toastOptions?.actionButtonStyle}\n                  removeToast={removeToast}\n                  toasts={toasts.filter((t) => t.position == toast.position)}\n                  heights={heights.filter((h) => h.position == toast.position)}\n                  setHeights={setHeights}\n                  expandByDefault={expand}\n                  gap={gap}\n                  loadingIcon={loadingIcon}\n                  expanded={expanded}\n                  pauseWhenPageIsHidden={pauseWhenPageIsHidden}\n                  cn={cn}\n                />\n              ))}\n          </ol>\n        );\n      })}\n    </section>\n  );\n});\nexport { toast, Toaster, type ExternalToast, type ToastT, type ToasterProps, useSonner };\nexport { type ToastClassnames, type ToastToDismiss, type Action } from './types';\n", "'use client';\nimport React from 'react';\nimport type { ToastTypes } from './types';\n\nexport const getAsset = (type: ToastTypes): JSX.Element | null => {\n  switch (type) {\n    case 'success':\n      return SuccessIcon;\n\n    case 'info':\n      return InfoIcon;\n\n    case 'warning':\n      return WarningIcon;\n\n    case 'error':\n      return ErrorIcon;\n\n    default:\n      return null;\n  }\n};\n\nconst bars = Array(12).fill(0);\n\nexport const Loader = ({ visible, className }: { visible: boolean, className?: string }) => {\n  return (\n    <div className={['sonner-loading-wrapper', className].filter(Boolean).join(' ')} data-visible={visible}>\n      <div className=\"sonner-spinner\">\n        {bars.map((_, i) => (\n          <div className=\"sonner-loading-bar\" key={`spinner-bar-${i}`} />\n        ))}\n      </div>\n    </div>\n  );\n};\n\nconst SuccessIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst WarningIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst InfoIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst ErrorIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nexport const CloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"12\"\n    height=\"12\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"1.5\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n    <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n  </svg>\n);\n", "import React from 'react';\n\nexport const useIsDocumentHidden = () => {\n  const [isDocumentHidden, setIsDocumentHidden] = React.useState(document.hidden);\n\n  React.useEffect(() => {\n    const callback = () => {\n      setIsDocumentHidden(document.hidden);\n    };\n    document.addEventListener('visibilitychange', callback);\n    return () => window.removeEventListener('visibilitychange', callback);\n  }, []);\n\n  return isDocumentHidden;\n};\n", "import type { ExternalToast, PromiseD<PERSON>, PromiseT, ToastT, ToastToDismiss, ToastTypes } from './types';\n\nimport React from 'react';\n\nlet toastsCounter = 1;\n\ntype titleT = (() => React.ReactNode) | React.ReactNode;\n\nclass Observer {\n  subscribers: Array<(toast: ExternalToast | ToastToDismiss) => void>;\n  toasts: Array<ToastT | ToastToDismiss>;\n\n  constructor() {\n    this.subscribers = [];\n    this.toasts = [];\n  }\n\n  // We use arrow functions to maintain the correct `this` reference\n  subscribe = (subscriber: (toast: ToastT | ToastToDismiss) => void) => {\n    this.subscribers.push(subscriber);\n\n    return () => {\n      const index = this.subscribers.indexOf(subscriber);\n      this.subscribers.splice(index, 1);\n    };\n  };\n\n  publish = (data: ToastT) => {\n    this.subscribers.forEach((subscriber) => subscriber(data));\n  };\n\n  addToast = (data: ToastT) => {\n    this.publish(data);\n    this.toasts = [...this.toasts, data];\n  };\n\n  create = (\n    data: ExternalToast & {\n      message?: titleT;\n      type?: ToastTypes;\n      promise?: PromiseT;\n      jsx?: React.ReactElement;\n    },\n  ) => {\n    const { message, ...rest } = data;\n    const id = typeof data?.id === 'number' || data.id?.length > 0 ? data.id : toastsCounter++;\n    const alreadyExists = this.toasts.find((toast) => {\n      return toast.id === id;\n    });\n    const dismissible = data.dismissible === undefined ? true : data.dismissible;\n\n    if (alreadyExists) {\n      this.toasts = this.toasts.map((toast) => {\n        if (toast.id === id) {\n          this.publish({ ...toast, ...data, id, title: message });\n          return {\n            ...toast,\n            ...data,\n            id,\n            dismissible,\n            title: message,\n          };\n        }\n\n        return toast;\n      });\n    } else {\n      this.addToast({ title: message, ...rest, dismissible, id });\n    }\n\n    return id;\n  };\n\n  dismiss = (id?: number | string) => {\n    if (!id) {\n      this.toasts.forEach((toast) => {\n        this.subscribers.forEach((subscriber) => subscriber({ id: toast.id, dismiss: true }));\n      });\n    }\n\n    this.subscribers.forEach((subscriber) => subscriber({ id, dismiss: true }));\n    return id;\n  };\n\n  message = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, message });\n  };\n\n  error = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, message, type: 'error' });\n  };\n\n  success = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'success', message });\n  };\n\n  info = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'info', message });\n  };\n\n  warning = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'warning', message });\n  };\n\n  loading = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'loading', message });\n  };\n\n  promise = <ToastData>(promise: PromiseT<ToastData>, data?: PromiseData<ToastData>) => {\n    if (!data) {\n      // Nothing to show\n      return;\n    }\n\n    let id: string | number | undefined = undefined;\n    if (data.loading !== undefined) {\n      id = this.create({\n        ...data,\n        promise,\n        type: 'loading',\n        message: data.loading,\n        description: typeof data.description !== 'function' ? data.description : undefined,\n      });\n    }\n\n    const p = promise instanceof Promise ? promise : promise();\n\n    let shouldDismiss = id !== undefined;\n    let result: ['resolve', ToastData] | ['reject', unknown];\n\n    const originalPromise = p\n      .then(async (response) => {\n        result = ['resolve', response];\n        const isReactElementResponse = React.isValidElement(response);\n        if (isReactElementResponse) {\n          shouldDismiss = false;\n          this.create({ id, type: 'default', message: response });\n        } else if (isHttpResponse(response) && !response.ok) {\n          shouldDismiss = false;\n          const message =\n            typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n          const description =\n            typeof data.description === 'function'\n              ? await data.description(`HTTP error! status: ${response.status}`)\n              : data.description;\n          this.create({ id, type: 'error', message, description });\n        } else if (data.success !== undefined) {\n          shouldDismiss = false;\n          const message = typeof data.success === 'function' ? await data.success(response) : data.success;\n          const description =\n            typeof data.description === 'function' ? await data.description(response) : data.description;\n          this.create({ id, type: 'success', message, description });\n        }\n      })\n      .catch(async (error) => {\n        result = ['reject', error];\n        if (data.error !== undefined) {\n          shouldDismiss = false;\n          const message = typeof data.error === 'function' ? await data.error(error) : data.error;\n          const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n          this.create({ id, type: 'error', message, description });\n        }\n      })\n      .finally(() => {\n        if (shouldDismiss) {\n          // Toast is still in load state (and will be indefinitely — dismiss it)\n          this.dismiss(id);\n          id = undefined;\n        }\n\n        data.finally?.();\n      });\n\n    const unwrap = () =>\n      new Promise<ToastData>((resolve, reject) =>\n        originalPromise.then(() => (result[0] === 'reject' ? reject(result[1]) : resolve(result[1]))).catch(reject),\n      );\n\n    if (typeof id !== 'string' && typeof id !== 'number') {\n      // cannot Object.assign on undefined\n      return { unwrap };\n    } else {\n      return Object.assign(id, { unwrap });\n    }\n  };\n\n  custom = (jsx: (id: number | string) => React.ReactElement, data?: ExternalToast) => {\n    const id = data?.id || toastsCounter++;\n    this.create({ jsx: jsx(id), id, ...data });\n    return id;\n  };\n}\n\nexport const ToastState = new Observer();\n\n// bind this to the toast function\nconst toastFunction = (message: titleT, data?: ExternalToast) => {\n  const id = data?.id || toastsCounter++;\n\n  ToastState.addToast({\n    title: message,\n    ...data,\n    id,\n  });\n  return id;\n};\n\nconst isHttpResponse = (data: any): data is Response => {\n  return (\n    data &&\n    typeof data === 'object' &&\n    'ok' in data &&\n    typeof data.ok === 'boolean' &&\n    'status' in data &&\n    typeof data.status === 'number'\n  );\n};\n\nconst basicToast = toastFunction;\n\nconst getHistory = () => ToastState.toasts;\n\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nexport const toast = Object.assign(\n  basicToast,\n  {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading,\n  },\n  { getHistory },\n);\n", "\n          export default function styleInject(css, { insertAt } = {}) {\n            if (!css || typeof document === 'undefined') return\n          \n            const head = document.head || document.getElementsByTagName('head')[0]\n            const style = document.createElement('style')\n            style.type = 'text/css'\n          \n            if (insertAt === 'top') {\n              if (head.firstChild) {\n                head.insertBefore(style, head.firstChild)\n              } else {\n                head.appendChild(style)\n              }\n            } else {\n              head.appendChild(style)\n            }\n          \n            if (style.styleSheet) {\n              style.styleSheet.cssText = css\n            } else {\n              style.appendChild(document.createTextNode(css))\n            }\n          }\n          ", "import styleInject from '#style-inject';styleInject(\":where(html[dir=\\\"ltr\\\"]),:where([data-sonner-toaster][dir=\\\"ltr\\\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\\\"rtl\\\"]),:where([data-sonner-toaster][dir=\\\"rtl\\\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=\\\"true\\\"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted=\\\"true\\\"]){transform:none}}:where([data-sonner-toaster][data-x-position=\\\"right\\\"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position=\\\"left\\\"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position=\\\"center\\\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\\\"top\\\"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position=\\\"bottom\\\"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\\\"true\\\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\\\"top\\\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\\\"bottom\\\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\\\"true\\\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\\\"dark\\\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\\\"true\\\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\\\"true\\\"]):before{content:\\\"\\\";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\\\"top\\\"][data-swiping=\\\"true\\\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\\\"bottom\\\"][data-swiping=\\\"true\\\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\\\"false\\\"][data-removed=\\\"true\\\"]):before{content:\\\"\\\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\\\"\\\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\\\"true\\\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\\\"false\\\"][data-front=\\\"false\\\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\\\"false\\\"][data-front=\\\"false\\\"][data-styled=\\\"true\\\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\\\"false\\\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\\\"true\\\"][data-expanded=\\\"true\\\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"true\\\"][data-swipe-out=\\\"false\\\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"][data-swipe-out=\\\"false\\\"][data-expanded=\\\"true\\\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"][data-swipe-out=\\\"false\\\"][data-expanded=\\\"false\\\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\\n\")", "import React from 'react';\n\nexport type ToastTypes = 'normal' | 'action' | 'success' | 'info' | 'warning' | 'error' | 'loading' | 'default';\n\nexport type PromiseT<Data = any> = Promise<Data> | (() => Promise<Data>);\n\nexport type PromiseTResult<Data = any> =\n  | string\n  | React.ReactNode\n  | ((data: Data) => React.ReactNode | string | Promise<React.ReactNode | string>);\n\nexport type PromiseExternalToast = Omit<ExternalToast, 'description'>;\n\nexport type PromiseData<ToastData = any> = PromiseExternalToast & {\n  loading?: string | React.ReactNode;\n  success?: PromiseTResult<ToastData>;\n  error?: PromiseTResult;\n  description?: PromiseTResult;\n  finally?: () => void | Promise<void>;\n};\n\nexport interface ToastClassnames {\n  toast?: string;\n  title?: string;\n  description?: string;\n  loader?: string;\n  closeButton?: string;\n  cancelButton?: string;\n  actionButton?: string;\n  success?: string;\n  error?: string;\n  info?: string;\n  warning?: string;\n  loading?: string;\n  default?: string;\n  content?: string;\n  icon?: string;\n}\n\nexport interface ToastIcons {\n  success?: React.ReactNode;\n  info?: React.ReactNode;\n  warning?: React.ReactNode;\n  error?: React.ReactNode;\n  loading?: React.ReactNode;\n  close?: React.ReactNode;\n}\n\nexport interface Action {\n  label: React.ReactNode;\n  onClick: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;\n  actionButtonStyle?: React.CSSProperties;\n}\n\nexport interface ToastT {\n  id: number | string;\n  title?: (() => React.ReactNode) | React.ReactNode;\n  type?: ToastTypes;\n  icon?: React.ReactNode;\n  jsx?: React.ReactNode;\n  richColors?: boolean;\n  invert?: boolean;\n  closeButton?: boolean;\n  dismissible?: boolean;\n  description?: (() => React.ReactNode) | React.ReactNode;\n  duration?: number;\n  delete?: boolean;\n  action?: Action | React.ReactNode;\n  cancel?: Action | React.ReactNode;\n  onDismiss?: (toast: ToastT) => void;\n  onAutoClose?: (toast: ToastT) => void;\n  promise?: PromiseT;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  style?: React.CSSProperties;\n  unstyled?: boolean;\n  className?: string;\n  classNames?: ToastClassnames;\n  descriptionClassName?: string;\n  position?: Position;\n}\n\nexport function isAction(action: Action | React.ReactNode): action is Action {\n  return (action as Action).label !== undefined;\n}\n\nexport type Position = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';\nexport interface HeightT {\n  height: number;\n  toastId: number | string;\n  position: Position;\n}\n\ninterface ToastOptions {\n  className?: string;\n  closeButton?: boolean;\n  descriptionClassName?: string;\n  style?: React.CSSProperties;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  duration?: number;\n  unstyled?: boolean;\n  classNames?: ToastClassnames;\n}\n\ntype CnFunction = (...classes: Array<string | undefined>) => string;\n\nexport interface ToasterProps {\n  invert?: boolean;\n  theme?: 'light' | 'dark' | 'system';\n  position?: Position;\n  hotkey?: string[];\n  richColors?: boolean;\n  expand?: boolean;\n  duration?: number;\n  gap?: number;\n  visibleToasts?: number;\n  closeButton?: boolean;\n  toastOptions?: ToastOptions;\n  className?: string;\n  style?: React.CSSProperties;\n  offset?: string | number;\n  dir?: 'rtl' | 'ltr' | 'auto';\n  /**\n   * @deprecated Please use the `icons` prop instead:\n   * ```jsx\n   * <Toaster\n   *   icons={{ loading: <LoadingIcon /> }}\n   * />\n   * ```\n   */\n  loadingIcon?: React.ReactNode;\n  icons?: ToastIcons;\n  containerAriaLabel?: string;\n  pauseWhenPageIsHidden?: boolean;\n  cn?: CnFunction;\n}\n\nexport interface ToastProps {\n  toast: ToastT;\n  toasts: ToastT[];\n  index: number;\n  expanded: boolean;\n  invert: boolean;\n  heights: HeightT[];\n  setHeights: React.Dispatch<React.SetStateAction<HeightT[]>>;\n  removeToast: (toast: ToastT) => void;\n  gap?: number;\n  position: Position;\n  visibleToasts: number;\n  expandByDefault: boolean;\n  closeButton: boolean;\n  interacting: boolean;\n  style?: React.CSSProperties;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  duration?: number;\n  className?: string;\n  unstyled?: boolean;\n  descriptionClassName?: string;\n  loadingIcon?: React.ReactNode;\n  classNames?: ToastClassnames;\n  icons?: ToastIcons;\n  closeButtonAriaLabel?: string;\n  pauseWhenPageIsHidden: boolean;\n  cn: CnFunction;\n  defaultRichColors?: boolean;\n}\n\nexport enum SwipeStateTypes {\n  SwipedOut = 'SwipedOut',\n  SwipedBack = 'SwipedBack',\n  NotSwiped = 'NotSwiped',\n}\n\nexport type Theme = 'light' | 'dark';\n\nexport interface ToastToDismiss {\n  id: number | string;\n  dismiss: boolean;\n}\n\nexport type ExternalToast = Omit<ToastT, 'id' | 'type' | 'title' | 'jsx' | 'delete' | 'promise'> & {\n  id?: number | string;\n};\n"], "names": ["React", "forwardRef", "ReactDOM", "React", "getAsset", "type", "SuccessIcon", "InfoIcon", "WarningIcon", "ErrorIcon", "bars", "Loader", "visible", "className", "_", "i", "CloseIcon", "React", "useIsDocumentHidden", "isDocumentHidden", "setIsDocumentHidden", "callback", "React", "toastsCounter", "Observer", "subscriber", "index", "data", "_a", "message", "rest", "id", "alreadyExists", "toast", "dismissible", "promise", "p", "<PERSON><PERSON><PERSON><PERSON>", "result", "originalPromise", "response", "isHttpResponse", "description", "error", "unwrap", "resolve", "reject", "jsx", "ToastState", "toastFunction", "basicToast", "getHistory", "styleInject", "css", "insertAt", "head", "style", "styleInject", "isAction", "action", "VISIBLE_TOASTS_AMOUNT", "VIEWPORT_OFFSET", "TOAST_LIFETIME", "TOAST_WIDTH", "GAP", "SWIPE_THRESHOLD", "TIME_BEFORE_UNMOUNT", "_cn", "classes", "Toast", "props", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "_j", "_k", "ToasterInvert", "toast", "unstyled", "interacting", "setHeights", "visibleToasts", "heights", "index", "toasts", "expanded", "removeToast", "defaultRichColors", "closeButtonFromToaster", "style", "cancelButtonStyle", "actionButtonStyle", "className", "descriptionClassName", "durationFromToaster", "position", "gap", "loadingIconProp", "expandByDefault", "classNames", "icons", "closeButtonAriaLabel", "pauseWhenPageIsHidden", "cn", "mounted", "setMounted", "React", "removed", "setRemoved", "swiping", "setSwiping", "swipeOut", "setSwipeOut", "isSwiped", "setIsSwiped", "offsetBeforeRemove", "setOffsetBeforeRemove", "initialHeight", "setInitialHeight", "remainingTime", "dragStartTime", "toastRef", "isFront", "isVisible", "toastType", "dismissible", "toastClassname", "toastDescriptionClassname", "heightIndex", "height", "closeButton", "duration", "closeTimerStartTimeRef", "offset", "lastCloseTimerStartTimeRef", "pointerStartRef", "y", "x", "toastsHeightBefore", "prev", "curr", "reducerIndex", "isDocumentHidden", "useIsDocumentHidden", "invert", "disabled", "toastNode", "h", "originalHeight", "newHeight", "deleteToast", "timeoutId", "elapsedTime", "getLoadingIcon", "Loader", "event", "swipeAmount", "timeTaken", "velocity", "yPosition", "isHighlighted", "CloseIcon", "getAsset", "isAction", "getDocumentDirection", "dirAttribute", "useSonner", "activeToasts", "setActiveToasts", "ToastState", "currentToasts", "t", "existingToastIndex", "updatedToasts", "Toaster", "forwardRef", "ref", "hotkey", "expand", "theme", "richColors", "toastOptions", "dir", "loadingIcon", "containerAriaLabel", "setToasts", "possiblePositions", "setExpanded", "setInteracting", "actualTheme", "setActualTheme", "listRef", "hotkeyLabel", "lastFocusedElementRef", "isFocusWithinRef", "toast<PERSON>oRemove", "id", "ReactDOM", "indexOfExistingToast", "darkMediaQuery", "matches", "error", "e", "handleKeyDown", "key"], "mappings": ";;;;;AAEA,OAAOA,GAAS,cAAAC,OAAkB;AAClC,OAAOC,OAAc,YCFrB,OAAOC,MAAW;;;;;AAGX,IAAMC,KAAYC,GAAyC;IAChE,OAAQA,EAAM;QACZ,KAAK;YACH,OAAOC;QAET,KAAK;YACH,OAAOC;QAET,KAAK;YACH,OAAOC;QAET,KAAK;YACH,OAAOC;QAET;YACE,OAAO;IACX;AACF,GAEMC,KAAO,MAAM,EAAE,EAAE,IAAA,CAAK,CAAC,GAEhBC,KAAS,CAAC,EAAE,SAAAC,CAAAA,EAAS,WAAAC,CAAU,EAAA,yMAExCV,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAW;YAAC;YAA0BU,CAAS;SAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QAAG,gBAAcD;IAAAA,yMAC7FT,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAU;IAAA,GACZO,GAAK,GAAA,CAAI,CAACI,GAAGC,0MACZZ,UAAAA,CAAA,aAAA,CAAC,OAAA;YAAI,WAAU;YAAqB,KAAK,CAAA,YAAA,EAAeY,GAAAA;QAAAA,CAAK,CAC9D,CACH,CACF,GAIET,2MACJH,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,yMAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGIK,2MACJL,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,yMAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGII,2MACJJ,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,yMAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGIM,2MACJN,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,yMAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGWa,2MACXb,UAAAA,CAAA,aAAA,CAAC,OAAA;IACC,OAAM;IACN,OAAM;IACN,QAAO;IACP,SAAQ;IACR,MAAK;IACL,QAAO;IACP,aAAY;IACZ,eAAc;IACd,gBAAe;AAAA,yMAEfA,UAAAA,CAAA,aAAA,CAAC,QAAA;IAAK,IAAG;IAAK,IAAG;IAAI,IAAG;IAAI,IAAG;AAAA,CAAK,yMACpCA,UAAAA,CAAA,aAAA,CAAC,QAAA;IAAK,IAAG;IAAI,IAAG;IAAI,IAAG;IAAK,IAAG;AAAA,CAAK,CACtC,EC3FF,OAAOc,OAAW;;AAEX,IAAMC,KAAsB,IAAM;IACvC,IAAM,CAACC,GAAkBC,CAAmB,CAAA,yMAAIH,UAAAA,CAAM,QAAA,CAAS,SAAS,MAAM;IAE9E,6MAAAA,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAMI,IAAW,IAAM;YACrBD,EAAoB,SAAS,MAAM;QACrC;QACA,OAAA,SAAS,gBAAA,CAAiB,oBAAoBC,CAAQ,GAC/C,IAAM,OAAO,mBAAA,CAAoB,oBAAoBA,CAAQ;IACtE,GAAG,CAAC,CAAC,GAEEF;AACT,ECZA,OAAOG,OAAW;;AAElB,IAAIC,KAAgB,GAIdC,KAAN,KAAe;IAIb,aAAc;QAMd,IAAA,CAAA,SAAA,IAAaC,IAAAA,CACX,IAAA,CAAK,WAAA,CAAY,IAAA,CAAKA,CAAU,GAEzB,IAAM;gBACX,IAAMC,IAAQ,IAAA,CAAK,WAAA,CAAY,OAAA,CAAQD,CAAU;gBACjD,IAAA,CAAK,WAAA,CAAY,MAAA,CAAOC,GAAO,CAAC;YAClC,CAAA;QAGF,IAAA,CAAA,OAAA,IAAWC,GAAiB;YAC1B,IAAA,CAAK,WAAA,CAAY,OAAA,EAASF,IAAeA,EAAWE,CAAI,CAAC;QAC3D;QAEA,IAAA,CAAA,QAAA,IAAYA,GAAiB;YAC3B,IAAA,CAAK,OAAA,CAAQA,CAAI,GACjB,IAAA,CAAK,MAAA,GAAS,CAAC;mBAAG,IAAA,CAAK,MAAA;gBAAQA,CAAI;;QACrC;QAEA,IAAA,CAAA,MAAA,IACEA,GAMG;YA3CP,IAAAC;YA4CI,IAAM,EAAE,SAAAC,CAAAA,EAAS,GAAGC,CAAK,EAAA,GAAIH,GACvBI,IAAK,OAAA,CAAOJ,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAO,YAAA,CAAA,CAAYC,IAAAD,EAAK,EAAA,KAAL,OAAA,KAAA,IAAAC,EAAS,MAAA,IAAS,IAAID,EAAK,EAAA,GAAKJ,MACrES,IAAgB,IAAA,CAAK,MAAA,CAAO,IAAA,EAAMC,IAC/BA,EAAM,EAAA,KAAOF,CACrB,GACKG,IAAcP,EAAK,WAAA,KAAgB,KAAA,IAAY,CAAA,IAAOA,EAAK,WAAA;YAEjE,OAAIK,IACF,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,GAAA,EAAKC,IACzBA,EAAM,EAAA,KAAOF,IAAAA,CACf,IAAA,CAAK,OAAA,CAAQ;oBAAE,GAAGE,CAAAA;oBAAO,GAAGN,CAAAA;oBAAM,IAAAI;oBAAI,OAAOF;gBAAQ,CAAC,GAC/C;oBACL,GAAGI,CAAAA;oBACH,GAAGN,CAAAA;oBACH,IAAAI;oBACA,aAAAG;oBACA,OAAOL;gBACT,CAAA,IAGKI,CACR,IAED,IAAA,CAAK,QAAA,CAAS;gBAAE,OAAOJ;gBAAS,GAAGC,CAAAA;gBAAM,aAAAI;gBAAa,IAAAH;YAAG,CAAC,GAGrDA;QACT;QAEA,IAAA,CAAA,OAAA,GAAWA,KAAAA,CACJA,KACH,IAAA,CAAK,MAAA,CAAO,OAAA,EAASE,GAAU;gBAC7B,IAAA,CAAK,WAAA,CAAY,OAAA,EAASR,IAAeA,EAAW;wBAAE,IAAIQ,EAAM,EAAA;wBAAI,SAAS,CAAA;oBAAK,CAAC,CAAC;YACtF,CAAC,GAGH,IAAA,CAAK,WAAA,CAAY,OAAA,EAASR,IAAeA,EAAW;oBAAE,IAAAM;oBAAI,SAAS,CAAA;gBAAK,CAAC,CAAC,GACnEA,CAAAA;QAGT,IAAA,CAAA,OAAA,GAAU,CAACF,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,SAAAE;YAAQ,CAAC;QAGzC,IAAA,CAAA,KAAA,GAAQ,CAACA,GAAmCF,IACnC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,SAAAE;gBAAS,MAAM;YAAQ,CAAC;QAGxD,IAAA,CAAA,OAAA,GAAU,CAACA,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAW,SAAAE;YAAQ,CAAC;QAG1D,IAAA,CAAA,IAAA,GAAO,CAACA,GAAmCF,IAClC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAQ,SAAAE;YAAQ,CAAC;QAGvD,IAAA,CAAA,OAAA,GAAU,CAACA,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAW,SAAAE;YAAQ,CAAC;QAG1D,IAAA,CAAA,OAAA,GAAU,CAACA,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAW,SAAAE;YAAQ,CAAC;QAG1D,IAAA,CAAA,OAAA,GAAU,CAAYM,GAA8BR,IAAkC;YACpF,IAAI,CAACA,GAEH;YAGF,IAAII;YACAJ,EAAK,OAAA,KAAY,KAAA,KAAA,CACnBI,IAAK,IAAA,CAAK,MAAA,CAAO;gBACf,GAAGJ,CAAAA;gBACH,SAAAQ;gBACA,MAAM;gBACN,SAASR,EAAK,OAAA;gBACd,aAAa,OAAOA,EAAK,WAAA,IAAgB,aAAaA,EAAK,WAAA,GAAc,KAAA;YAC3E,CAAC,CAAA;YAGH,IAAMS,IAAID,aAAmB,UAAUA,IAAUA,EAAQ,GAErDE,IAAgBN,MAAO,KAAA,GACvBO,GAEEC,IAAkBH,EACrB,IAAA,CAAK,OAAOI,GAAa;gBAGxB,IAFAF,IAAS;oBAAC;oBAAWE,CAAQ;iBAAA,uMACElB,WAAAA,CAAM,cAAA,CAAekB,CAAQ,GAE1DH,IAAgB,CAAA,GAChB,IAAA,CAAK,MAAA,CAAO;oBAAE,IAAAN;oBAAI,MAAM;oBAAW,SAASS;gBAAS,CAAC;qBAAA,IAC7CC,GAAeD,CAAQ,KAAK,CAACA,EAAS,EAAA,EAAI;oBACnDH,IAAgB,CAAA;oBAChB,IAAMR,IACJ,OAAOF,EAAK,KAAA,IAAU,aAAa,MAAMA,EAAK,KAAA,CAAM,CAAA,oBAAA,EAAuBa,EAAS,MAAA,EAAQ,IAAIb,EAAK,KAAA,EACjGe,IACJ,OAAOf,EAAK,WAAA,IAAgB,aACxB,MAAMA,EAAK,WAAA,CAAY,CAAA,oBAAA,EAAuBa,EAAS,MAAA,EAAQ,IAC/Db,EAAK,WAAA;oBACX,IAAA,CAAK,MAAA,CAAO;wBAAE,IAAAI;wBAAI,MAAM;wBAAS,SAAAF;wBAAS,aAAAa;oBAAY,CAAC;gBAAA,OAAA,IAC9Cf,EAAK,OAAA,KAAY,KAAA,GAAW;oBACrCU,IAAgB,CAAA;oBAChB,IAAMR,IAAU,OAAOF,EAAK,OAAA,IAAY,aAAa,MAAMA,EAAK,OAAA,CAAQa,CAAQ,IAAIb,EAAK,OAAA,EACnFe,IACJ,OAAOf,EAAK,WAAA,IAAgB,aAAa,MAAMA,EAAK,WAAA,CAAYa,CAAQ,IAAIb,EAAK,WAAA;oBACnF,IAAA,CAAK,MAAA,CAAO;wBAAE,IAAAI;wBAAI,MAAM;wBAAW,SAAAF;wBAAS,aAAAa;oBAAY,CAAC;gBAAA;YAE7D,CAAC,EACA,KAAA,CAAM,OAAOC,GAAU;gBAEtB,IADAL,IAAS;oBAAC;oBAAUK,CAAK;iBAAA,EACrBhB,EAAK,KAAA,KAAU,KAAA,GAAW;oBAC5BU,IAAgB,CAAA;oBAChB,IAAMR,IAAU,OAAOF,EAAK,KAAA,IAAU,aAAa,MAAMA,EAAK,KAAA,CAAMgB,CAAK,IAAIhB,EAAK,KAAA,EAC5Ee,IAAc,OAAOf,EAAK,WAAA,IAAgB,aAAa,MAAMA,EAAK,WAAA,CAAYgB,CAAK,IAAIhB,EAAK,WAAA;oBAClG,IAAA,CAAK,MAAA,CAAO;wBAAE,IAAAI;wBAAI,MAAM;wBAAS,SAAAF;wBAAS,aAAAa;oBAAY,CAAC;gBAAA;YAE3D,CAAC,EACA,OAAA,CAAQ,IAAM;gBAnKrB,IAAAd;gBAoKYS,KAAAA,CAEF,IAAA,CAAK,OAAA,CAAQN,CAAE,GACfA,IAAK,KAAA,CAAA,GAAA,CAGPH,IAAAD,EAAK,OAAA,KAAL,QAAAC,EAAA,IAAA,CAAAD;YACF,CAAC,GAEGiB,IAAS,IACb,IAAI,QAAmB,CAACC,GAASC,IAC/BP,EAAgB,IAAA,CAAK,IAAOD,CAAAA,CAAO,CAAC,CAAA,KAAM,WAAWQ,EAAOR,CAAAA,CAAO,CAAC,CAAC,IAAIO,EAAQP,CAAAA,CAAO,CAAC,CAAC,CAAE,EAAE,KAAA,CAAMQ,CAAM,CAC5G;YAEF,OAAI,OAAOf,KAAO,YAAY,OAAOA,KAAO,WAEnC;gBAAE,QAAAa;YAAO,IAET,OAAO,MAAA,CAAOb,GAAI;gBAAE,QAAAa;YAAO,CAAC;QAEvC;QAEA,IAAA,CAAA,MAAA,GAAS,CAACG,GAAkDpB,IAAyB;YACnF,IAAMI,IAAAA,CAAKJ,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAMJ;YACvB,OAAA,IAAA,CAAK,MAAA,CAAO;gBAAE,KAAKwB,EAAIhB,CAAE;gBAAG,IAAAA;gBAAI,GAAGJ;YAAK,CAAC,GAClCI;QACT;QAjLE,IAAA,CAAK,WAAA,GAAc,CAAC,CAAA,EACpB,IAAA,CAAK,MAAA,GAAS,CAAC;IACjB;AAgLF,GAEaiB,IAAa,IAAIxB,IAGxByB,KAAgB,CAACpB,GAAiBF,IAAyB;IAC/D,IAAMI,IAAAA,CAAKJ,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAMJ;IAEvB,OAAAyB,EAAW,QAAA,CAAS;QAClB,OAAOnB;QACP,GAAGF,CAAAA;QACH,IAAAI;IACF,CAAC,GACMA;AACT,GAEMU,MAAkBd,IAEpBA,KACA,OAAOA,KAAS,YAChB,QAAQA,KACR,OAAOA,EAAK,EAAA,IAAO,aACnB,YAAYA,KACZ,OAAOA,EAAK,MAAA,IAAW,UAIrBuB,KAAaD,IAEbE,KAAa,IAAMH,EAAW,MAAA,EAGvBf,KAAQ,OAAO,MAAA,CAC1BiB,IACA;IACE,SAASF,EAAW,OAAA;IACpB,MAAMA,EAAW,IAAA;IACjB,SAASA,EAAW,OAAA;IACpB,OAAOA,EAAW,KAAA;IAClB,QAAQA,EAAW,MAAA;IACnB,SAASA,EAAW,OAAA;IACpB,SAASA,EAAW,OAAA;IACpB,SAASA,EAAW,OAAA;IACpB,SAASA,EAAW;AACtB,GACA;IAAE,YAAAG;AAAW,CACf;AC5OyB,SAARC,GAA6BC,CAAAA,EAAK,EAAE,UAAAC,CAAS,EAAA,GAAI,CAAC,CAAA,CAAG;IAC1D,IAAI,CAACD,KAAO,OAAO,YAAa,aAAa;IAE7C,IAAME,IAAO,SAAS,IAAA,IAAQ,SAAS,oBAAA,CAAqB,MAAM,CAAA,CAAE,CAAC,CAAA,EAC/DC,IAAQ,SAAS,aAAA,CAAc,OAAO;IAC5CA,EAAM,IAAA,GAAO,YAETF,MAAa,SACXC,EAAK,UAAA,GACPA,EAAK,YAAA,CAAaC,GAAOD,EAAK,UAAU,IAK1CA,EAAK,WAAA,CAAYC,CAAK,GAGpBA,EAAM,UAAA,GACRA,EAAM,UAAA,CAAW,OAAA,GAAUH,IAE3BG,EAAM,WAAA,CAAY,SAAS,cAAA,CAAeH,CAAG,CAAC;AAElD;ACvB8BI,GAAY,CAAA;AAAA,CAA2na;ACkFxqa,SAASC,EAASC,CAAAA,CAAoD;IAC3E,OAAQA,EAAkB,KAAA,KAAU,KAAA;AACtC;ANhEA,IAAMC,KAAwB,GAGxBC,KAAkB,QAGlBC,KAAiB,KAGjBC,KAAc,KAGdC,KAAM,IAGNC,KAAkB,IAGlBC,KAAsB;AAE5B,SAASC,GAAAA,GAAOC,CAAAA,CAAiC;IAC/C,OAAOA,EAAQ,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;AACzC;AAEA,IAAMC,MAASC,GAAsB;IA5CrC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC;IA6CE,IAAM,EACJ,QAAQC,CAAAA,EACR,OAAAC,CAAAA,EACA,UAAAC,CAAAA,EACA,aAAAC,CAAAA,EACA,YAAAC,CAAAA,EACA,eAAAC,CAAAA,EACA,SAAAC,CAAAA,EACA,OAAAC,CAAAA,EACA,QAAAC,CAAAA,EACA,UAAAC,CAAAA,EACA,aAAAC,CAAAA,EACA,mBAAAC,CAAAA,EACA,aAAaC,CAAAA,EACb,OAAAC,EAAAA,EACA,mBAAAC,CAAAA,EACA,mBAAAC,CAAAA,EACA,WAAAC,IAAY,EAAA,EACZ,sBAAAC,KAAuB,EAAA,EACvB,UAAUC,CAAAA,EACV,UAAAC,EAAAA,EACA,KAAAC,EAAAA,EACA,aAAaC,CAAAA,EACb,iBAAAC,CAAAA,EACA,YAAAC,CAAAA,EACA,OAAAC,CAAAA,EACA,sBAAAC,IAAuB,aAAA,EACvB,uBAAAC,CAAAA,EACA,IAAAC,CACF,EAAA,GAAIvC,GACE,CAACwC,GAASC,CAAU,CAAA,GAAIC,gNAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC5C,CAACC,GAASC,EAAU,CAAA,yMAAIF,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC5C,CAACG,GAASC,CAAU,CAAA,GAAIJ,gNAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC5C,CAACK,IAAUC,CAAW,CAAA,yMAAIN,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC9C,CAACO,GAAUC,EAAW,CAAA,wMAAIR,WAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC9C,CAACS,GAAoBC,CAAqB,CAAA,yMAAIV,UAAAA,CAAM,QAAA,CAAS,CAAC,GAC9D,CAACW,GAAeC,CAAgB,CAAA,GAAIZ,gNAAAA,CAAM,QAAA,CAAS,CAAC,GACpDa,0MAAgBb,UAAAA,CAAM,MAAA,CAAO7B,EAAM,QAAA,IAAYiB,KAAuBtC,EAAc,GACpFgE,0MAAgBd,UAAAA,CAAM,MAAA,CAAoB,IAAI,GAC9Ce,yMAAWf,WAAAA,CAAM,MAAA,CAAsB,IAAI,GAC3CgB,KAAUvC,MAAU,GACpBwC,KAAYxC,IAAQ,KAAKF,GACzB2C,IAAY/C,EAAM,IAAA,EAClBgD,IAAchD,EAAM,WAAA,KAAgB,CAAA,GACpCiD,KAAiBjD,EAAM,SAAA,IAAa,IACpCkD,KAA4BlD,EAAM,oBAAA,IAAwB,IAE1DmD,2MAActB,UAAAA,CAAM,OAAA,CACxB,IAAMxB,EAAQ,SAAA,EAAW+C,IAAWA,EAAO,OAAA,KAAYpD,EAAM,EAAE,KAAK,GACpE;QAACK;QAASL,EAAM,EAAE;KACpB,GACMqD,2MAAcxB,UAAAA,CAAM,OAAA,CACxB,IAAG;QAjGP,IAAAzC;QAiGU,OAAA,CAAAA,IAAAY,EAAM,WAAA,KAAN,OAAAZ,IAAqBuB;IAAAA,GAC3B;QAACX,EAAM,WAAA;QAAaW,CAAsB;KAC5C,GACM2C,2MAAWzB,UAAAA,CAAM,OAAA,CACrB,IAAM7B,EAAM,QAAA,IAAYiB,KAAuBtC,IAC/C;QAACqB,EAAM,QAAA;QAAUiB,CAAmB;KACtC,GACMsC,2MAAyB1B,UAAAA,CAAM,MAAA,CAAO,CAAC,GACvC2B,0MAAS3B,UAAAA,CAAM,MAAA,CAAO,CAAC,GACvB4B,0MAA6B5B,WAAAA,CAAM,MAAA,CAAO,CAAC,GAC3C6B,2MAAkB7B,UAAAA,CAAM,MAAA,CAAwC,IAAI,GACpE,CAAC8B,IAAGC,EAAC,CAAA,GAAI1C,GAAS,KAAA,CAAM,GAAG,GAC3B2C,2MAAqBhC,UAAAA,CAAM,OAAA,CAAQ,IAChCxB,EAAQ,MAAA,CAAO,CAACyD,GAAMC,GAAMC,IAE7BA,KAAgBb,KACXW,IAGFA,IAAOC,EAAK,MAAA,EAClB,CAAC,GACH;QAAC1D;QAAS8C,EAAW;KAAC,GACnBc,KAAmBC,GAAoB,GAEvCC,KAASnE,EAAM,MAAA,IAAUD,GACzBqE,KAAWrB,MAAc;IAE/BS,EAAO,OAAA,yMAAU3B,UAAAA,CAAM,OAAA,CAAQ,IAAMsB,KAAchC,KAAM0C,IAAoB;QAACV;QAAaU,EAAkB;KAAC,yMAE9GhC,UAAAA,CAAM,SAAA,CAAU,IAAM;QAEpBD,EAAW,CAAA,CAAI;IACjB,GAAG,CAAC,CAAC,yMAELC,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAMwC,IAAYzB,EAAS,OAAA;QAC3B,IAAIyB,GAAW;YACb,IAAMjB,IAASiB,EAAU,qBAAA,CAAsB,EAAE,MAAA;YAEjD,OAAA5B,EAAiBW,CAAM,GACvBjD,GAAYmE,IAAM;oBAAC;wBAAE,SAAStE,EAAM,EAAA;wBAAI,QAAAoD;wBAAQ,UAAUpD,EAAM;oBAAS,EAAG;uBAAGsE,CAAC;iBAAC,GAC1E,IAAMnE,EAAYmE,KAAMA,EAAE,MAAA,EAAQlB,IAAWA,EAAO,OAAA,KAAYpD,EAAM,EAAE,CAAC;QAAA;IAEpF,GAAG;QAACG;QAAYH,EAAM,EAAE;KAAC,GAEzB6B,gNAAAA,CAAM,eAAA,CAAgB,IAAM;QAC1B,IAAI,CAACF,GAAS;QACd,IAAM0C,IAAYzB,EAAS,OAAA,EACrB2B,IAAiBF,EAAU,KAAA,CAAM,MAAA;QACvCA,EAAU,KAAA,CAAM,MAAA,GAAS;QACzB,IAAMG,IAAYH,EAAU,qBAAA,CAAsB,EAAE,MAAA;QACpDA,EAAU,KAAA,CAAM,MAAA,GAASE,GAEzB9B,EAAiB+B,CAAS,GAE1BrE,GAAYE,IACYA,EAAQ,IAAA,CAAM+C,KAAWA,EAAO,OAAA,KAAYpD,EAAM,EAAE,IAIjEK,EAAQ,GAAA,EAAK+C,IAAYA,EAAO,OAAA,KAAYpD,EAAM,EAAA,GAAK;oBAAE,GAAGoD,CAAAA;oBAAQ,QAAQoB;gBAAU,IAAIpB,CAAO,IAFjG;gBAAC;oBAAE,SAASpD,EAAM,EAAA;oBAAI,QAAQwE;oBAAW,UAAUxE,EAAM;gBAAS,EAAG;mBAAGK,CAAO;aAIzF;IACH,GAAG;QAACsB;QAAS3B,EAAM,KAAA;QAAOA,EAAM,WAAA;QAAaG;QAAYH,EAAM,EAAE;KAAC;IAElE,IAAMyE,IAAc5C,gNAAAA,CAAM,WAAA,CAAY,IAAM;QAE1CE,GAAW,CAAA,CAAI,GACfQ,EAAsBiB,EAAO,OAAO,GACpCrD,GAAYmE,IAAMA,EAAE,MAAA,EAAQlB,IAAWA,EAAO,OAAA,KAAYpD,EAAM,EAAE,CAAC,GAEnE,WAAW,IAAM;YACfS,EAAYT,CAAK;QACnB,GAAGjB,EAAmB;IACxB,GAAG;QAACiB;QAAOS;QAAaN;QAAYqD,CAAM;KAAC;0MAE3C3B,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAK7B,EAAM,OAAA,IAAW+C,MAAc,aAAc/C,EAAM,QAAA,KAAa,IAAA,KAAYA,EAAM,IAAA,KAAS,WAAW;QAC3G,IAAI0E;QA6BJ,OAAIlE,KAAYN,KAAgBuB,KAAyBwC,KAAAA,CA1BtC,IAAM;YACvB,IAAIR,GAA2B,OAAA,GAAUF,GAAuB,OAAA,EAAS;gBAEvE,IAAMoB,IAAc,IAAI,KAAK,EAAE,OAAA,CAAQ,IAAIpB,GAAuB,OAAA;gBAElEb,EAAc,OAAA,GAAUA,EAAc,OAAA,GAAUiC;YAAAA;YAGlDlB,GAA2B,OAAA,GAAU,IAAI,KAAK,EAAE,OAAA,CAAQ;QAC1D,CAAA,EAkBa,IAAA,CAhBM,IAAM;YAInBf,EAAc,OAAA,KAAY,IAAA,KAAA,CAE9Ba,GAAuB,OAAA,GAAU,IAAI,KAAK,EAAE,OAAA,CAAQ,GAGpDmB,IAAY,WAAW,IAAM;gBAtMnC,IAAAtF;gBAAAA,CAuMQA,IAAAY,EAAM,WAAA,KAAN,QAAAZ,EAAA,IAAA,CAAAY,GAAoBA,IACpByE,EAAY;YACd,GAAG/B,EAAc,OAAO,CAAA;QAC1B,CAAA,EAKa,GAGN,IAAM,aAAagC,CAAS;IACrC,GAAG;QAAClE;QAAUN;QAAaF;QAAO+C;QAAWtB;QAAuBwC;QAAkBQ,CAAW;KAAC,yMAElG5C,UAAAA,CAAM,SAAA,CAAU,IAAM;QAChB7B,EAAM,MAAA,IACRyE,EAAY;IAEhB,GAAG;QAACA;QAAazE,EAAM,MAAM;KAAC;IAE9B,SAAS4E,IAAiB;QA3N5B,IAAAxF,GAAAC,GAAAC;QA4NI,OAAIiC,KAAA,QAAAA,EAAO,OAAA,yMAEPM,UAAAA,CAAA,aAAA,CAAC,OAAA;YACC,WAAWH,EAAGJ,KAAA,OAAA,KAAA,IAAAA,EAAY,MAAA,EAAA,CAAQlC,IAAAY,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAZ,EAAmB,MAAA,EAAQ,eAAe;YAC5E,gBAAc2D,MAAc;QAAA,GAE3BxB,EAAM,OACT,IAIAH,0MAEAS,UAAAA,CAAA,aAAA,CAAC,OAAA;YACC,WAAWH,EAAGJ,KAAA,OAAA,KAAA,IAAAA,EAAY,MAAA,EAAA,CAAQjC,IAAAW,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAX,EAAmB,MAAA,EAAQ,eAAe;YAC5E,gBAAc0D,MAAc;QAAA,GAE3B3B,CACH,0MAGGS,UAAAA,CAAA,aAAA,CAACgD,IAAA;YAAO,WAAWnD,EAAGJ,KAAA,OAAA,KAAA,IAAAA,EAAY,MAAA,EAAA,CAAQhC,IAAAU,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAV,EAAmB,MAAM;YAAG,SAASyD,MAAc;QAAA,CAAW;IACjH;IAEA,OACElB,gNAAAA,CAAA,aAAA,CAAC,MAAA;QACC,UAAU;QACV,KAAKe;QACL,WAAWlB,EACTX,GACAkC,IACA3B,KAAA,OAAA,KAAA,IAAAA,EAAY,KAAA,EAAA,CACZlC,KAAAY,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAZ,GAAmB,KAAA,EACnBkC,KAAA,OAAA,KAAA,IAAAA,EAAY,OAAA,EACZA,KAAA,OAAA,KAAA,IAAAA,CAAAA,CAAayB,EAAAA,EAAAA,CACb1D,KAAAW,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAX,EAAAA,CAAoB0D,EACtB;QACA,qBAAkB;QAClB,oBAAA,CAAkBzD,KAAAU,EAAM,UAAA,KAAN,OAAAV,KAAoBoB;QACtC,eAAa,CAAA,CAASV,EAAM,GAAA,IAAOA,EAAM,QAAA,IAAYC,CAAAA;QACrD,gBAAc0B;QACd,gBAAc,CAAA,CAAQ3B,EAAM,OAAA;QAC5B,eAAaoC;QACb,gBAAcN;QACd,gBAAcgB;QACd,mBAAiBa;QACjB,mBAAiBC;QACjB,cAAYtD;QACZ,cAAYuC;QACZ,gBAAcb;QACd,oBAAkBgB;QAClB,aAAWD;QACX,eAAaoB;QACb,kBAAgBjC;QAChB,iBAAe,CAAA,CAAA,CAAQ1B,KAAaa,KAAmBM,CAAAA;QACvD,OACE;YACE,WAAWrB;YACX,mBAAmBA;YACnB,aAAaC,EAAO,MAAA,GAASD;YAC7B,YAAY,GAAGwB,IAAUQ,IAAqBkB,EAAO,OAAA,CAAA,EAAA,CAAA;YACrD,oBAAoBnC,IAAkB,SAAS,GAAGmB,EAAAA,EAAAA,CAAAA;YAClD,GAAG5B,EAAAA;YACH,GAAGZ,EAAM,KACX;;QAEF,gBAAgB8E,GAAU;YACpBV,MAAY,CAACpB,KAAAA,CACjBL,EAAc,OAAA,GAAU,IAAI,MAC5BJ,EAAsBiB,EAAO,OAAO,GAEnCsB,EAAM,MAAA,CAAuB,iBAAA,CAAkBA,EAAM,SAAS,GAC1DA,EAAM,MAAA,CAAuB,OAAA,KAAY,YAAA,CAC9C7C,EAAW,CAAA,CAAI,GACfyB,GAAgB,OAAA,GAAU;gBAAE,GAAGoB,EAAM,OAAA;gBAAS,GAAGA,EAAM,OAAQ;aAAA,CAAA;QACjE;QACA,aAAa,IAAM;YAxSzB,IAAA1F,GAAAC,GAAAC,GAAAC;YAySQ,IAAI2C,MAAY,CAACc,GAAa;YAE9BU,GAAgB,OAAA,GAAU;YAC1B,IAAMqB,IAAc,OAAA,CAAA,CAAO3F,IAAAwD,EAAS,OAAA,KAAT,OAAA,KAAA,IAAAxD,EAAkB,KAAA,CAAM,gBAAA,CAAiB,kBAAkB,OAAA,CAAQ,MAAM,GAAA,KAAO,CAAC,GACtG4F,IAAY,IAAI,KAAK,EAAE,OAAA,CAAQ,IAAA,CAAA,CAAI3F,IAAAsD,EAAc,OAAA,KAAd,OAAA,KAAA,IAAAtD,EAAuB,OAAA,EAAA,GAC1D4F,IAAW,KAAK,GAAA,CAAIF,CAAW,IAAIC;YAGzC,IAAI,KAAK,GAAA,CAAID,CAAW,KAAKjG,MAAmBmG,IAAW,KAAM;gBAC/D1C,EAAsBiB,EAAO,OAAO,GAAA,CACpClE,IAAAU,EAAM,SAAA,KAAN,QAAAV,EAAA,IAAA,CAAAU,GAAkBA,IAClByE,EAAY,GACZtC,EAAY,CAAA,CAAI,GAChBE,GAAY,CAAA,CAAK;gBACjB;YAAA;YAAA,CAGF9C,IAAAqD,EAAS,OAAA,KAAT,QAAArD,EAAkB,KAAA,CAAM,WAAA,CAAY,kBAAkB,QACtD0C,EAAW,CAAA,CAAK;QAClB;QACA,gBAAgB6C,GAAU;YA7ThC,IAAA1F,GAAAC,GAAAC;YA8TQ,IAAI,CAACoE,GAAgB,OAAA,IAAW,CAACV,GAAa;YAE9C,IAAMkC,IAAYJ,EAAM,OAAA,GAAUpB,GAAgB,OAAA,CAAQ,CAAA,EACpDyB,IAAAA,CAAAA,CAAgB/F,IAAA,OAAO,YAAA,CAAa,CAAA,KAApB,OAAA,KAAA,IAAAA,EAAuB,QAAA,GAAW,MAAA,IAAS;YAC7C,OAAA,CAAA,CAAOC,IAAAuD,EAAS,OAAA,KAAT,OAAA,KAAA,IAAAvD,EAAkB,KAAA,CAAM,gBAAA,CAAiB,kBAAkB,OAAA,CAAQ,MAAM,GAAA,KAAO,CAAC,IAE1F,KAChBgD,GAAY,CAAA,CAAI,GAGd,CAAA8C,KAAAA,CAAAA,CAEJ7F,IAAAsD,EAAS,OAAA,KAAT,QAAAtD,EAAkB,KAAA,CAAM,WAAA,CAAY,kBAAkB,GAAG,KAAK,GAAA,CAAI,GAAG4F,CAAS,EAAA,EAAA,CAAA,CAAA;QAChF;IAAA,GAEC7B,MAAe,CAACrD,EAAM,GAAA,yMACrB6B,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,cAAYL;QACZ,iBAAe4C;QACf,qBAAiB,CAAA;QACjB,SACEA,MAAY,CAACpB,IACT,IAAM,CAAC,IACP,IAAM;YArVtB,IAAA5D;YAsVkBqF,EAAY,GAAA,CACZrF,IAAAY,EAAM,SAAA,KAAN,QAAAZ,EAAA,IAAA,CAAAY,GAAkBA;QACpB;QAEN,WAAW0B,EAAGJ,KAAA,OAAA,KAAA,IAAAA,EAAY,WAAA,EAAA,CAAa/B,KAAAS,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAT,GAAmB,WAAW;IAAA,GAAA,CAEpEC,KAAA+B,KAAA,OAAA,KAAA,IAAAA,EAAO,KAAA,KAAP,OAAA/B,KAAgB4F,EACnB,IACE,MAEHpF,EAAM,GAAA,0MAAO6B,UAAAA,CAAM,cAAA,CAAe7B,EAAM,KAAK,IAC5CA,EAAM,GAAA,GACJA,EAAM,GAAA,GACJ,OAAOA,EAAM,KAAA,IAAU,aACzBA,EAAM,KAAA,CAAM,IAEZA,EAAM,KAAA,yMAGR6B,UAAAA,CAAA,aAAA,uMAAAA,UAAAA,CAAA,QAAA,EAAA,MACGkB,KAAa/C,EAAM,IAAA,IAAQA,EAAM,OAAA,yMAChC6B,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,aAAU;QAAG,WAAWH,EAAGJ,KAAA,OAAA,KAAA,IAAAA,EAAY,IAAA,EAAA,CAAM7B,KAAAO,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAP,GAAmB,IAAI;IAAA,GACtEO,EAAM,OAAA,IAAYA,EAAM,IAAA,KAAS,aAAa,CAACA,EAAM,IAAA,GAAQA,EAAM,IAAA,IAAQ4E,GAAe,IAAI,MAC9F5E,EAAM,IAAA,KAAS,YAAYA,EAAM,IAAA,IAAA,CAAQuB,KAAA,OAAA,KAAA,IAAAA,CAAAA,CAAQwB,EAAAA,KAAcsC,GAAStC,CAAS,IAAI,IACxF,IACE,4MAEJlB,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,gBAAa;QAAG,WAAWH,EAAGJ,KAAA,OAAA,KAAA,IAAAA,EAAY,OAAA,EAAA,CAAS5B,KAAAM,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAN,GAAmB,OAAO;IAAA,yMAChFmC,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,cAAW;QAAG,WAAWH,EAAGJ,KAAA,OAAA,KAAA,IAAAA,EAAY,KAAA,EAAA,CAAO3B,KAAAK,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAL,GAAmB,KAAK;IAAA,GACzE,OAAOK,EAAM,KAAA,IAAU,aAAaA,EAAM,KAAA,CAAM,IAAIA,EAAM,KAC7D,GACCA,EAAM,WAAA,yMACL6B,UAAAA,CAAA,aAAA,CAAC,OAAA;QACC,oBAAiB;QACjB,WAAWH,EACTV,IACAkC,IACA5B,KAAA,OAAA,KAAA,IAAAA,EAAY,WAAA,EAAA,CACZ1B,KAAAI,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAJ,GAAmB,WACrB;IAAA,GAEC,OAAOI,EAAM,WAAA,IAAgB,aAAaA,EAAM,WAAA,CAAY,IAAIA,EAAM,WACzE,IACE,IACN,yMACC6B,UAAAA,CAAM,cAAA,CAAe7B,EAAM,MAAM,IAChCA,EAAM,MAAA,GACJA,EAAM,MAAA,IAAUsF,EAAStF,EAAM,MAAM,0MACvC6B,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,eAAW,CAAA;QACX,eAAW,CAAA;QACX,OAAO7B,EAAM,iBAAA,IAAqBa;QAClC,UAAUiE,GAAU;YA1YlC,IAAA1F,GAAAC;YA4YqBiG,EAAStF,EAAM,MAAM,KACrBgD,KAAAA,CAAAA,CACL3D,IAAAA,CAAAD,IAAAY,EAAM,MAAA,EAAO,OAAA,KAAb,QAAAX,EAAA,IAAA,CAAAD,GAAuB0F,IACvBL,EAAY,CAAA;QACd;QACA,WAAW/C,EAAGJ,KAAA,OAAA,KAAA,IAAAA,EAAY,YAAA,EAAA,CAAczB,KAAAG,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAH,GAAmB,YAAY;IAAA,GAEtEG,EAAM,MAAA,CAAO,KAChB,IACE,4MACH6B,UAAAA,CAAM,cAAA,CAAe7B,EAAM,MAAM,IAChCA,EAAM,MAAA,GACJA,EAAM,MAAA,IAAUsF,EAAStF,EAAM,MAAM,0MACvC6B,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,eAAW,CAAA;QACX,eAAW,CAAA;QACX,OAAO7B,EAAM,iBAAA,IAAqBc;QAClC,UAAUgE,GAAU;YA7ZlC,IAAA1F,GAAAC;YA+ZqBiG,EAAStF,EAAM,MAAM,KAAA,CAAA,CAC1BX,IAAAA,CAAAD,IAAAY,EAAM,MAAA,EAAO,OAAA,KAAb,QAAAX,EAAA,IAAA,CAAAD,GAAuB0F,IACnB,CAAAA,EAAM,gBAAA,IACVL,EAAY,CAAA;QACd;QACA,WAAW/C,EAAGJ,KAAA,OAAA,KAAA,IAAAA,EAAY,YAAA,EAAA,CAAcxB,KAAAE,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAF,GAAmB,YAAY;IAAA,GAEtEE,EAAM,MAAA,CAAO,KAChB,IACE,IACN,CAEJ;AAEJ;AAEA,SAASuF,IAA4C;IAEnD,IADI,OAAO,UAAW,eAClB,OAAO,YAAa,aAAa,OAAO;IAE5C,IAAMC,IAAe,SAAS,eAAA,CAAgB,YAAA,CAAa,KAAK;IAEhE,OAAIA,MAAiB,UAAU,CAACA,IACvB,OAAO,gBAAA,CAAiB,SAAS,eAAe,EAAE,SAAA,GAGpDA;AACT;AAEA,SAASC,IAAY;IACnB,IAAM,CAACC,GAAcC,CAAe,CAAA,yMAAI9D,UAAAA,CAAM,QAAA,CAAmB,CAAC,CAAC;IAEnE,6MAAAA,UAAAA,CAAM,SAAA,CAAU,IACP+D,EAAW,SAAA,CAAW5F,GAAU;YACrC2F,GAAiBE,GAAkB;gBACjC,IAAI,aAAa7F,KAASA,EAAM,OAAA,EAC9B,OAAO6F,EAAc,MAAA,EAAQC,IAAMA,EAAE,EAAA,KAAO9F,EAAM,EAAE;gBAGtD,IAAM+F,IAAqBF,EAAc,SAAA,EAAWC,IAAMA,EAAE,EAAA,KAAO9F,EAAM,EAAE;gBAC3E,IAAI+F,MAAuB,CAAA,GAAI;oBAC7B,IAAMC,IAAgB,CAAC;2BAAGH,CAAa;qBAAA;oBACvC,OAAAG,CAAAA,CAAcD,CAAkB,CAAA,GAAI;wBAAE,GAAGC,CAAAA,CAAcD,CAAkB,CAAA;wBAAG,GAAG/F;oBAAM,GAC9EgG;gBAAAA,KAEP,EAAA,OAAO;oBAAChG,EAAO;uBAAG6F,CAAa;;YAEnC,CAAC;QACH,CAAC,GACA,CAAC,CAAC,GAEE;QACL,QAAQH;IACV;AACF;AAEA,IAAMO,+MAAUC,aAAAA,EAAsC,SAAiB/G,CAAAA,EAAOgH,CAAAA,CAAK;IACjF,IAAM,EACJ,QAAAhC,CAAAA,EACA,UAAAjD,IAAW,cAAA,EACX,QAAAkF,IAAS;QAAC;QAAU,MAAM;KAAA,EAC1B,QAAAC,CAAAA,EACA,aAAAhD,CAAAA,EACA,WAAAtC,CAAAA,EACA,QAAAyC,CAAAA,EACA,OAAA8C,IAAQ,OAAA,EACR,YAAAC,CAAAA,EACA,UAAAjD,CAAAA,EACA,OAAA1C,CAAAA,EACA,eAAAR,KAAgB3B,EAAAA,EAChB,cAAA+H,CAAAA,EACA,KAAAC,IAAMlB,GAAqB,CAAA,EAC3B,KAAApE,IAAMtC,EAAAA,EACN,aAAA6H,EAAAA,EACA,OAAAnF,CAAAA,EACA,oBAAAoF,KAAqB,eAAA,EACrB,uBAAAlF,EAAAA,EACA,IAAAC,IAAK1C,EACP,EAAA,GAAIG,GACE,CAACoB,GAAQqG,CAAS,CAAA,yMAAI/E,UAAAA,CAAM,QAAA,CAAmB,CAAC,CAAC,GACjDgF,0MAAoBhF,UAAAA,CAAM,OAAA,CAAQ,IAC/B,MAAM,IAAA,CACX,IAAI,IAAI;YAACX,CAAQ;SAAA,CAAE,MAAA,CAAOX,EAAO,MAAA,EAAQP,IAAUA,EAAM,QAAQ,EAAE,GAAA,EAAKA,IAAUA,EAAM,QAAQ,CAAC,CAAC,CACpG,GACC;QAACO;QAAQW,CAAQ;KAAC,GACf,CAACb,GAASF,CAAU,CAAA,yMAAI0B,UAAAA,CAAM,QAAA,CAAoB,CAAC,CAAC,GACpD,CAACrB,GAAUsG,CAAW,CAAA,yMAAIjF,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC9C,CAAC3B,GAAa6G,CAAc,CAAA,yMAAIlF,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GACpD,CAACmF,IAAaC,CAAc,CAAA,GAAIpF,gNAAAA,CAAM,QAAA,CAC1CyE,MAAU,WACNA,IACA,OAAO,UAAW,eAClB,OAAO,UAAA,IAAc,OAAO,UAAA,CAAW,8BAA8B,EAAE,OAAA,GACrE,SAEF,OACN,GAEMY,0MAAUrF,UAAAA,CAAM,MAAA,CAAyB,IAAI,GAC7CsF,KAAcf,EAAO,IAAA,CAAK,GAAG,EAAE,OAAA,CAAQ,QAAQ,EAAE,EAAE,OAAA,CAAQ,UAAU,EAAE,GACvEgB,0MAAwBvF,UAAAA,CAAM,MAAA,CAAoB,IAAI,GACtDwF,0MAAmBxF,UAAAA,CAAM,MAAA,CAAO,CAAA,CAAK,GAErCpB,0MAAcoB,WAAAA,CAAM,WAAA,CAAayF,GAA0B;QAC/DV,GAAWrG,GAAW;YAvgB1B,IAAAnB;YAwgBM,OAAA,CAAKA,IAAAmB,EAAO,IAAA,EAAMP,IAAUA,EAAM,EAAA,KAAOsH,EAAc,EAAE,CAAA,KAApD,QAAAlI,EAAuD,MAAA,IAC1DwG,EAAW,OAAA,CAAQ0B,EAAc,EAAE,GAG9B/G,EAAO,MAAA,CAAO,CAAC,EAAE,IAAAgH,CAAG,EAAA,GAAMA,MAAOD,EAAc,EAAE;QAC1D,CAAC;IACH,GAAG,CAAC,CAAC;IAEL,6MAAAzF,UAAAA,CAAM,SAAA,CAAU,IACP+D,EAAW,SAAA,EAAW5F,GAAU;YACrC,IAAKA,EAAyB,OAAA,EAAS;gBACrC4G,EAAWrG,KAAWA,EAAO,GAAA,EAAKuF,IAAOA,EAAE,EAAA,KAAO9F,EAAM,EAAA,GAAK;4BAAE,GAAG8F,CAAAA;4BAAG,QAAQ,CAAA;wBAAK,IAAIA,CAAE,CAAC;gBACzF;YAAA;YAIF,WAAW,IAAM;6NACf0B,UAAAA,CAAS,SAAA,CAAU,IAAM;oBACvBZ,GAAWrG,GAAW;wBACpB,IAAMkH,IAAuBlH,EAAO,SAAA,CAAWuF,KAAMA,EAAE,EAAA,KAAO9F,EAAM,EAAE;wBAGtE,OAAIyH,MAAyB,CAAA,IACpB,CACL;+BAAGlH,EAAO,KAAA,CAAM,GAAGkH,CAAoB;4BACvC;gCAAE,GAAGlH,CAAAA,CAAOkH,CAAoB,CAAA;gCAAG,GAAGzH;4BAAM,EAC5C;+BAAGO,EAAO,KAAA,CAAMkH,IAAuB,CAAC,CAC1C;yBAAA,GAGK;4BAACzH,EAAO;+BAAGO,CAAM;;oBAC1B,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,GACA,CAAC,CAAC,yMAELsB,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAIyE,MAAU,UAAU;YACtBW,EAAeX,CAAK;YACpB;QAAA;QAcF,IAXIA,MAAU,YAAA,CAER,OAAO,UAAA,IAAc,OAAO,UAAA,CAAW,8BAA8B,EAAE,OAAA,GAEzEW,EAAe,MAAM,IAGrBA,EAAe,OAAO,CAAA,GAItB,OAAO,UAAW,aAAa;QACnC,IAAMS,IAAiB,OAAO,UAAA,CAAW,8BAA8B;QAEvE,IAAI;YAEFA,EAAe,gBAAA,CAAiB,UAAU,CAAC,EAAE,SAAAC,CAAQ,EAAA,GAAM;gBAEvDV,EADEU,IACa,SAEA,OAFM;YAIzB,CAAC;QACH,EAAA,OAASC,GAAP;YAEAF,EAAe,WAAA,CAAY,CAAC,EAAE,SAAAC,CAAQ,EAAA,GAAM;gBAC1C,IAAI;oBAEAV,EADEU,IACa,SAEA,OAFM;gBAIzB,EAAA,OAASE,GAAP;oBACA,QAAQ,KAAA,CAAMA,CAAC;gBACjB;YACF,CAAC;QACH;IACF,GAAG;QAACvB,CAAK;KAAC,yMAEVzE,UAAAA,CAAM,SAAA,CAAU,IAAM;QAEhBtB,EAAO,MAAA,IAAU,KACnBuG,EAAY,CAAA,CAAK;IAErB,GAAG;QAACvG,CAAM;KAAC,yMAEXsB,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAMiG,KAAiBhD,GAAyB;YAlmBpD,IAAA1F,GAAAC;YAmmB8B+G,EAAO,KAAA,EAAO2B,IAASjD,CAAAA,CAAciD,CAAG,CAAA,IAAKjD,EAAM,IAAA,KAASiD,CAAG,KAAA,CAGrFjB,EAAY,CAAA,CAAI,GAAA,CAChB1H,IAAA8H,EAAQ,OAAA,KAAR,QAAA9H,EAAiB,KAAA,EAAA,GAIjB0F,EAAM,IAAA,KAAS,YAAA,CACd,SAAS,aAAA,KAAkBoC,EAAQ,OAAA,IAAA,CAAW7H,IAAA6H,EAAQ,OAAA,KAAR,QAAA7H,EAAiB,QAAA,CAAS,SAAS,aAAA,CAAA,KAElFyH,EAAY,CAAA,CAAK;QAErB;QACA,OAAA,SAAS,gBAAA,CAAiB,WAAWgB,CAAa,GAE3C,IAAM,SAAS,mBAAA,CAAoB,WAAWA,CAAa;IACpE,GAAG;QAAC1B,CAAM;KAAC,yMAEXvE,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAIqF,EAAQ,OAAA,EACV,OAAO,IAAM;YACPE,EAAsB,OAAA,IAAA,CACxBA,EAAsB,OAAA,CAAQ,KAAA,CAAM;gBAAE,eAAe,CAAA;YAAK,CAAC,GAC3DA,EAAsB,OAAA,GAAU,MAChCC,EAAiB,OAAA,GAAU,CAAA,CAAA;QAE/B;IAEJ,GAAG;QAACH,EAAQ,OAAO;KAAC,yMAIlBrF,UAAAA,CAAA,aAAA,CAAC,WAAA;QACC,cAAY,GAAG8E,GAAAA,CAAAA,EAAsBQ,IAAAA;QACrC,UAAU,CAAA;QACV,aAAU;QACV,iBAAc;QACd,eAAY;IAAA,GAEXN,EAAkB,GAAA,CAAI,CAAC3F,GAAUZ,IAAU;QA3oBlD,IAAAlB;QA4oBQ,IAAM,CAACuE,GAAGC,CAAC,CAAA,GAAI1C,EAAS,KAAA,CAAM,GAAG;QAEjC,OAAKX,EAAO,MAAA,yMAGVsB,UAAAA,CAAA,aAAA,CAAC,MAAA;YACC,KAAKX;YACL,KAAKuF,MAAQ,SAASlB,GAAqB,IAAIkB;YAC/C,UAAU,CAAA;YACV,KAAKS;YACL,WAAWnG;YACX,uBAAmB,CAAA;YACnB,cAAYiG;YACZ,mBAAiBrD;YACjB,eAAanD,KAAYD,EAAO,MAAA,GAAS,KAAK,CAAC8F;YAC/C,mBAAiBzC;YACjB,OACE;gBACE,wBAAwB,GAAA,CAAA,CAAGxE,IAAAiB,CAAAA,CAAQ,CAAC,CAAA,KAAT,OAAA,KAAA,IAAAjB,EAAY,MAAA,KAAU,EAAA,EAAA,CAAA;gBACjD,YAAY,OAAOoE,KAAW,WAAW,GAAGA,EAAAA,EAAAA,CAAAA,GAAaA,KAAU9E;gBACnE,WAAW,GAAGE,GAAAA,EAAAA,CAAAA;gBACd,SAAS,GAAGuC,EAAAA,EAAAA,CAAAA;gBACZ,GAAGP,CACL;;YAEF,SAASkE,GAAU;gBACbuC,EAAiB,OAAA,IAAW,CAACvC,EAAM,aAAA,CAAc,QAAA,CAASA,EAAM,aAAa,KAAA,CAC/EuC,EAAiB,OAAA,GAAU,CAAA,GACvBD,EAAsB,OAAA,IAAA,CACxBA,EAAsB,OAAA,CAAQ,KAAA,CAAM;oBAAE,eAAe,CAAA;gBAAK,CAAC,GAC3DA,EAAsB,OAAA,GAAU,IAAA,CAAA;YAGtC;YACA,UAAUtC,GAAU;gBAEhBA,EAAM,MAAA,YAAkB,eAAeA,EAAM,MAAA,CAAO,OAAA,CAAQ,WAAA,KAAgB,WAIzEuC,EAAiB,OAAA,IAAA,CACpBA,EAAiB,OAAA,GAAU,CAAA,GAC3BD,EAAsB,OAAA,GAAUtC,EAAM,aAAA;YAE1C;YACA,cAAc,IAAMgC,EAAY,CAAA,CAAI;YACpC,aAAa,IAAMA,EAAY,CAAA,CAAI;YACnC,cAAc,IAAM;gBAEb5G,KACH4G,EAAY,CAAA,CAAK;YAErB;YACA,eAAgBhC,GAAU;gBAEtBA,EAAM,MAAA,YAAkB,eAAeA,EAAM,MAAA,CAAO,OAAA,CAAQ,WAAA,KAAgB,WAG9EiC,EAAe,CAAA,CAAI;YACrB;YACA,aAAa,IAAMA,EAAe,CAAA,CAAK;QAAA,GAEtCxG,EACE,MAAA,EAAQP,IAAW,CAACA,EAAM,QAAA,IAAYM,MAAU,KAAMN,EAAM,QAAA,KAAakB,CAAQ,EACjF,GAAA,CAAI,CAAClB,GAAOM,IAAO;YA5sBlC,IAAAlB,IAAAC;YA6sBgB,6MAAAwC,UAAAA,CAAA,aAAA,CAAC3C,IAAA;gBACC,KAAKc,EAAM,EAAA;gBACX,OAAOuB;gBACP,OAAOjB;gBACP,OAAON;gBACP,mBAAmBuG;gBACnB,UAAA,CAAUnH,KAAAoH,KAAA,OAAA,KAAA,IAAAA,EAAc,QAAA,KAAd,OAAApH,KAA0BkE;gBACpC,WAAWkD,KAAA,OAAA,KAAA,IAAAA,EAAc,SAAA;gBACzB,sBAAsBA,KAAA,OAAA,KAAA,IAAAA,EAAc,oBAAA;gBACpC,QAAQrC;gBACR,eAAe/D;gBACf,aAAA,CAAaf,KAAAmH,KAAA,OAAA,KAAA,IAAAA,EAAc,WAAA,KAAd,OAAAnH,KAA6BgE;gBAC1C,aAAanD;gBACb,UAAUgB;gBACV,OAAOsF,KAAA,OAAA,KAAA,IAAAA,EAAc,KAAA;gBACrB,UAAUA,KAAA,OAAA,KAAA,IAAAA,EAAc,QAAA;gBACxB,YAAYA,KAAA,OAAA,KAAA,IAAAA,EAAc,UAAA;gBAC1B,mBAAmBA,KAAA,OAAA,KAAA,IAAAA,EAAc,iBAAA;gBACjC,mBAAmBA,KAAA,OAAA,KAAA,IAAAA,EAAc,iBAAA;gBACjC,aAAa/F;gBACb,QAAQF,EAAO,MAAA,EAAQuF,IAAMA,EAAE,QAAA,IAAY9F,EAAM,QAAQ;gBACzD,SAASK,EAAQ,MAAA,EAAQiE,IAAMA,EAAE,QAAA,IAAYtE,EAAM,QAAQ;gBAC3D,YAAYG;gBACZ,iBAAiBkG;gBACjB,KAAKlF;gBACL,aAAauF;gBACb,UAAUlG;gBACV,uBAAuBiB;gBACvB,IAAIC;YAAAA,CACN;QAAA,CACD,CACL,IA9FyB;IAgG7B,CAAC,CACH;AAEJ,CAAC", "ignoreList": [0, 1, 2, 3, 4, 5, 6], "debugId": null}}]}