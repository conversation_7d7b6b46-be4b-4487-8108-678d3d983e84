self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7fc009b51f4c5e2ee17c430a8158571716aac73af0\": {\n      \"workers\": {\n        \"app/(auth)/sign-in/[[...sign-in]]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/(auth)/sign-up/[[...sign-up]]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/sign-up/[[...sign-up]]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/sign-in/[[...sign-in]]/page\": \"action-browser\",\n        \"app/(auth)/sign-up/[[...sign-up]]/page\": \"action-browser\",\n        \"app/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"GNQz0lVyaaIc7f0c4yGD06irgeCKLswqReDQDriS71I=\"\n}"