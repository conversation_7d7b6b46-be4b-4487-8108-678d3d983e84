{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/lib/prisma.js"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\nexport const db = globalThis.prisma || new PrismaClient();\n\nif (process.env.NODE_ENV !== \"production\") {\n  globalThis.prisma = db;\n}\n\n// globalThis.prisma: This global variable ensures that the Prisma client instance is\n// reused across hot reloads during development. Without this, each time your application\n// reloads, a new instance of the Prisma client would be created, potentially leading\n// to connection issues.\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,KAAK,WAAW,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEvD,wCAA2C;IACzC,WAAW,MAAM,GAAG;AACtB,EAEA,qFAAqF;CACrF,yFAAyF;CACzF,qFAAqF;CACrF,wBAAwB", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/lib/checkUser.js"], "sourcesContent": ["import { currentUser } from \"@clerk/nextjs/server\";\nimport { db } from \"./prisma\";\n\nexport const checkUser = async () => {\n  const user = await currentUser();\n\n  if (!user) {\n    return null;\n  }\n\n  try {\n    const loggedInUser = await db.user.findUnique({\n      where: {\n        clerkUserId: user.id,\n      },\n    });\n\n    if (loggedInUser) {\n      return loggedInUser;\n    }\n\n    const name = `${user.firstName} ${user.lastName}`;\n\n    const newUser = await db.user.create({\n      data: {\n        clerkUserId: user.id,\n        name,\n        imageUrl: user.imageUrl,\n        email: user.emailAddresses[0].emailAddress,\n      },\n    });\n\n    return newUser;\n  } catch (error) {\n    console.log(error.message);\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,YAAY;IACvB,MAAM,OAAO,MAAM,CAAA,GAAA,0LAAA,CAAA,cAAW,AAAD;IAE7B,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,IAAI;QACF,MAAM,eAAe,MAAM,6GAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;YAC5C,OAAO;gBACL,aAAa,KAAK,EAAE;YACtB;QACF;QAEA,IAAI,cAAc;YAChB,OAAO;QACT;QAEA,MAAM,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;QAEjD,MAAM,UAAU,MAAM,6GAAA,CAAA,KAAE,CAAC,IAAI,CAAC,MAAM,CAAC;YACnC,MAAM;gBACJ,aAAa,KAAK,EAAE;gBACpB;gBACA,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,cAAc,CAAC,EAAE,CAAC,YAAY;YAC5C;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,MAAM,OAAO;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/button.jsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nconst Button = React.forwardRef(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/header.jsx"], "sourcesContent": ["import { checkUser } from \"@/lib/checkUser\";\nimport { SignedIn, SignedOut, SignInButton, UserButton } from \"@clerk/nextjs\";\nimport { LayoutDashboard, PenBox } from \"lucide-react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { Button } from \"./ui/button\";\n\nconst Header = async () => {\n  await checkUser();\n\n  return (\n    <header className=\"fixed top-0 w-full bg-white/95 backdrop-blur-md z-50 border-b border-gray-200/50 shadow-sm\">\n      <nav className=\"container mx-auto px-4 py-4 flex items-center justify-between\">\n        <Link href=\"/\" className=\"flex items-center space-x-2\">\n          <Image\n            src={\"/logo.jpg\"}\n            alt=\"AIFin Logo\"\n            width={200}\n            height={60}\n            className=\"h-10 w-auto object-contain\"\n          />\n          <span className=\"text-xl font-bold gradient-title hidden sm:block\">AIFin</span>\n        </Link>\n\n        {/* Navigation Links - Different for signed in/out users */}\n        <div className=\"hidden md:flex items-center space-x-8\">\n          <SignedOut>\n            <a href=\"#features\" className=\"text-gray-600 hover:text-blue-600 font-medium transition-colors\">\n              Features\n            </a>\n            <a\n              href=\"#testimonials\"\n              className=\"text-gray-600 hover:text-blue-600 font-medium transition-colors\"\n            >\n              Testimonials\n            </a>\n          </SignedOut>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex items-center space-x-3\">\n          <SignedIn>\n            <Link\n              href=\"/dashboard\"\n              className=\"text-gray-600 hover:text-blue-600 flex items-center gap-2\"\n            >\n              <Button variant=\"outline\" className=\"border-gray-300 hover:border-blue-500 hover:text-blue-600\">\n                <LayoutDashboard size={18} />\n                <span className=\"hidden md:inline\">Dashboard</span>\n              </Button>\n            </Link>\n            <Link href=\"/transaction/create\">\n              <Button className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 flex items-center gap-2 shadow-md\">\n                <PenBox size={18} />\n                <span className=\"hidden md:inline\">Add Transaction</span>\n              </Button>\n            </Link>\n          </SignedIn>\n          <SignedOut>\n            <SignInButton forceRedirectUrl=\"/dashboard\">\n              <Button variant=\"outline\" className=\"border-blue-500 text-blue-600 hover:bg-blue-50\">Login</Button>\n            </SignInButton>\n            <Link href=\"/dashboard\">\n              <Button className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-md\">\n                Get Started\n              </Button>\n            </Link>\n          </SignedOut>\n          <SignedIn>\n            <UserButton\n              appearance={{\n                elements: {\n                  avatarBox: \"w-10 h-10 ring-2 ring-blue-100\",\n                },\n              }}\n            />\n          </SignedIn>\n        </div>\n      </nav>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,SAAS;IACb,MAAM,CAAA,GAAA,gHAAA,CAAA,YAAS,AAAD;IAEd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;;sCACvB,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK;4BACL,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,8OAAC;4BAAK,WAAU;sCAAmD;;;;;;;;;;;;8BAIrE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yKAAA,CAAA,YAAS;;0CACR,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAAkE;;;;;;0CAGhG,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAOL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,yKAAA,CAAA,WAAQ;;8CACP,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,8OAAC,4NAAA,CAAA,kBAAe;gDAAC,MAAM;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;8CAGvC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,6MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;;0DACd,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;sCAIzC,8OAAC,yKAAA,CAAA,YAAS;;8CACR,8OAAC,sLAAA,CAAA,eAAY;oCAAC,kBAAiB;8CAC7B,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAAiD;;;;;;;;;;;8CAEvF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,WAAU;kDAAiG;;;;;;;;;;;;;;;;;sCAKvH,8OAAC,yKAAA,CAAA,WAAQ;sCACP,cAAA,8OAAC,sLAAA,CAAA,aAAU;gCACT,YAAY;oCACV,UAAU;wCACR,WAAW;oCACb;gCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;uCAEe", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_92243eee.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_92243eee-module__hBCtSW__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_92243eee.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/layout.js"], "sourcesContent": ["import Header from \"@/components/header\";\nimport { <PERSON><PERSON><PERSON><PERSON> } from \"@clerk/nextjs\";\nimport { Inter } from \"next/font/google\";\nimport { Toaster } from \"sonner\";\nimport \"./globals.css\";\n\nconst inter = Inter({ subsets: [\"latin\"] });\n\nexport const metadata = {\n  title: \"AIFin\",\n  description: \"One stop Finance Platform\",\n};\n\nexport default function RootLayout({ children }) {\n  return (\n    <ClerkProvider>\n      <html lang=\"en\">\n        <head>\n          <link rel=\"icon\" href=\"/logo.jpg\" sizes=\"any\" />\n        </head>\n        <body className={`${inter.className}`}>\n          <ErrorBoundary>\n            <Header />\n            <main className=\"min-h-screen\">{children}</main>\n            <Toaster richColors />\n          </ErrorBoundary>\n\n          <footer className=\"bg-gradient-to-br from-gray-50 to-blue-50 py-16 border-t border-gray-200\">\n            <div className=\"container mx-auto px-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\">\n                <div className=\"col-span-1 md:col-span-2\">\n                  <h3 className=\"text-2xl font-bold gradient-title mb-4\">AIFin</h3>\n                  <p className=\"text-gray-600 mb-4 max-w-md\">\n                    Transform your financial life with AI-powered insights and automated tools for smarter money management.\n                  </p>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-4\">Product</h4>\n                  <ul className=\"space-y-2 text-gray-600\">\n                    <li><a href=\"#features\" className=\"hover:text-blue-600 transition-colors\">Features</a></li>\n                    <li><a href=\"#\" className=\"hover:text-blue-600 transition-colors\">Pricing</a></li>\n                    <li><a href=\"#\" className=\"hover:text-blue-600 transition-colors\">Security</a></li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-4\">Company</h4>\n                  <ul className=\"space-y-2 text-gray-600\">\n                    <li><a href=\"#\" className=\"hover:text-blue-600 transition-colors\">About</a></li>\n                    <li><a href=\"#\" className=\"hover:text-blue-600 transition-colors\">Contact</a></li>\n                    <li><a href=\"#\" className=\"hover:text-blue-600 transition-colors\">Privacy</a></li>\n                  </ul>\n                </div>\n              </div>\n              <div className=\"border-t border-gray-200 pt-8 text-center text-gray-600\">\n                <p>© 2024 AIFin. Made with 💗 for better financial management.</p>\n              </div>\n            </div>\n          </footer>\n        </body>\n      </html>\n    </ClerkProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;AAEA;;;;;;;AAKO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC,yKAAA,CAAA,gBAAa;kBACZ,cAAA,8OAAC;YAAK,MAAK;;8BACT,8OAAC;8BACC,cAAA,8OAAC;wBAAK,KAAI;wBAAO,MAAK;wBAAY,OAAM;;;;;;;;;;;8BAE1C,8OAAC;oBAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,SAAS,EAAE;;sCACnC,8OAAC;;8CACC,8OAAC,qHAAA,CAAA,UAAM;;;;;8CACP,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC,wIAAA,CAAA,UAAO;oCAAC,UAAU;;;;;;;;;;;;sCAGrB,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAA8B;;;;;;;;;;;;0DAI7C,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG,cAAA,8OAAC;oEAAE,MAAK;oEAAY,WAAU;8EAAwC;;;;;;;;;;;0EAC1E,8OAAC;0EAAG,cAAA,8OAAC;oEAAE,MAAK;oEAAI,WAAU;8EAAwC;;;;;;;;;;;0EAClE,8OAAC;0EAAG,cAAA,8OAAC;oEAAE,MAAK;oEAAI,WAAU;8EAAwC;;;;;;;;;;;;;;;;;;;;;;;0DAGtE,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG,cAAA,8OAAC;oEAAE,MAAK;oEAAI,WAAU;8EAAwC;;;;;;;;;;;0EAClE,8OAAC;0EAAG,cAAA,8OAAC;oEAAE,MAAK;oEAAI,WAAU;8EAAwC;;;;;;;;;;;0EAClE,8OAAC;0EAAG,cAAA,8OAAC;oEAAE,MAAK;oEAAI,WAAU;8EAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAIxE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}]}