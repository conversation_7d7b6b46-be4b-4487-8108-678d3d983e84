{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/hooks/use-fetch.js"], "sourcesContent": ["import { useState } from \"react\";\nimport { toast } from \"sonner\";\n\nconst useFetch = (cb) => {\n  const [data, setData] = useState(undefined);\n  const [loading, setLoading] = useState(null);\n  const [error, setError] = useState(null);\n\n  const fn = async (...args) => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await cb(...args);\n      setData(response);\n      setError(null);\n    } catch (error) {\n      setError(error);\n      toast.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return { data, loading, error, fn, setData };\n};\n\nexport default useFetch;\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,KAAK,OAAO,GAAG;QACnB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,QAAQ;YACR,SAAS;QACX,EAAE,OAAO,OAAO;YACd,SAAS;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QAAE;QAAM;QAAS;QAAO;QAAI;IAAQ;AAC7C;uCAEe", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/button.jsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nconst Button = React.forwardRef(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/drawer.jsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Drawer as DrawerPrimitive } from \"vaul\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Drawer = ({\n  shouldScaleBackground = true,\n  ...props\n}) => (\n  <DrawerPrimitive.Root shouldScaleBackground={shouldScaleBackground} {...props} />\n)\nDrawer.displayName = \"Drawer\"\n\nconst DrawerTrigger = DrawerPrimitive.Trigger\n\nconst DrawerPortal = DrawerPrimitive.Portal\n\nconst DrawerClose = DrawerPrimitive.Close\n\nconst DrawerOverlay = React.forwardRef(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Overlay\n    ref={ref}\n    className={cn(\"fixed inset-0 z-50 bg-black/80\", className)}\n    {...props} />\n))\nDrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName\n\nconst DrawerContent = React.forwardRef(({ className, children, ...props }, ref) => (\n  <DrawerPortal>\n    <DrawerOverlay />\n    <DrawerPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background\",\n        className\n      )}\n      {...props}>\n      <div className=\"mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted\" />\n      {children}\n    </DrawerPrimitive.Content>\n  </DrawerPortal>\n))\nDrawerContent.displayName = \"DrawerContent\"\n\nconst DrawerHeader = ({\n  className,\n  ...props\n}) => (\n  <div\n    className={cn(\"grid gap-1.5 p-4 text-center sm:text-left\", className)}\n    {...props} />\n)\nDrawerHeader.displayName = \"DrawerHeader\"\n\nconst DrawerFooter = ({\n  className,\n  ...props\n}) => (\n  <div className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)} {...props} />\n)\nDrawerFooter.displayName = \"DrawerFooter\"\n\nconst DrawerTitle = React.forwardRef(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold leading-none tracking-tight\", className)}\n    {...props} />\n))\nDrawerTitle.displayName = DrawerPrimitive.Title.displayName\n\nconst DrawerDescription = React.forwardRef(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props} />\n))\nDrawerDescription.displayName = DrawerPrimitive.Description.displayName\n\nexport {\n  Drawer,\n  DrawerPortal,\n  DrawerOverlay,\n  DrawerTrigger,\n  DrawerClose,\n  DrawerContent,\n  DrawerHeader,\n  DrawerFooter,\n  DrawerTitle,\n  DrawerDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,SAAS,CAAC,EACd,wBAAwB,IAAI,EAC5B,GAAG,OACJ,iBACC,8OAAC,sIAAA,CAAA,SAAe,CAAC,IAAI;QAAC,uBAAuB;QAAwB,GAAG,KAAK;;;;;;AAE/E,OAAO,WAAW,GAAG;AAErB,MAAM,gBAAgB,sIAAA,CAAA,SAAe,CAAC,OAAO;AAE7C,MAAM,eAAe,sIAAA,CAAA,SAAe,CAAC,MAAM;AAE3C,MAAM,cAAc,sIAAA,CAAA,SAAe,CAAC,KAAK;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC/D,8OAAC,sIAAA,CAAA,SAAe,CAAC,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAC/C,GAAG,KAAK;;;;;;AAEb,cAAc,WAAW,GAAG,sIAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,sIAAA,CAAA,SAAe,CAAC,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;gBAED,GAAG,KAAK;;kCACT,8OAAC;wBAAI,WAAU;;;;;;oBACd;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG;AAE5B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAEb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAAa,GAAG,KAAK;;;;;;AAE7E,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC,sIAAA,CAAA,SAAe,CAAC,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;AAEb,YAAY,WAAW,GAAG,sIAAA,CAAA,SAAe,CAAC,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACnE,8OAAC,sIAAA,CAAA,SAAe,CAAC,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,kBAAkB,WAAW,GAAG,sIAAA,CAAA,SAAe,CAAC,WAAW,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/input.jsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef(({ className, type, ...props }, ref) => {\n  return (\n    (<input\n      type={type}\n      className={cn(\n        \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      ref={ref}\n      {...props} />)\n  );\n})\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC7D,qBACG,8OAAC;QACA,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAEf;AACA,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/select.jsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}>\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\n    {...props}>\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\"flex cursor-default items-center justify-center py-1\", className)}\n    {...props}>\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}>\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\"p-1\", position === \"popper\" &&\n          \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\")}>\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\n    {...props} />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}>\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props} />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2TACA;QAED,GAAG,KAAK;;YACR;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACtE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBACT,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBACT,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC9F,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BACT,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,OAAO,aAAa,YAChC;8BACD;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAEb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACtE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BACT,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACjE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAEb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/switch.jsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}>\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\n      )} />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxD,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+XACA;QAED,GAAG,KAAK;QACT,KAAK;kBACL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAIR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/actions/dashboard.js"], "sourcesContent": ["\"use server\";\n\nimport aj from \"@/lib/arcjet\";\nimport { db } from \"@/lib/prisma\";\nimport { request } from \"@arcjet/next\";\nimport { auth } from \"@clerk/nextjs/server\";\nimport { revalidatePath } from \"next/cache\";\n\nconst serializeTransaction = (obj) => {\n  const serialized = { ...obj };\n  if (obj.balance) {\n    serialized.balance = obj.balance.toNumber();\n  }\n  if (obj.amount) {\n    serialized.amount = obj.amount.toNumber();\n  }\n  return serialized;\n};\n\nexport async function getUserAccounts() {\n  const { userId } = await auth();\n  if (!userId) throw new Error(\"Unauthorized\");\n\n  const user = await db.user.findUnique({\n    where: { clerkUserId: userId },\n  });\n\n  if (!user) {\n    throw new Error(\"User not found\");\n  }\n\n  try {\n    const accounts = await db.account.findMany({\n      where: { userId: user.id },\n      orderBy: { createdAt: \"desc\" },\n      include: {\n        _count: {\n          select: {\n            transactions: true,\n          },\n        },\n      },\n    });\n\n    // Serialize accounts before sending to client\n    const serializedAccounts = accounts.map(serializeTransaction);\n\n    return serializedAccounts;\n  } catch (error) {\n    console.error(error.message);\n  }\n}\n\nexport async function createAccount(data) {\n  try {\n    const { userId } = await auth();\n    if (!userId) throw new Error(\"Unauthorized\");\n\n    // Get request data for ArcJet\n    const req = await request();\n\n    // Check rate limit\n    const decision = await aj.protect(req, {\n      userId,\n      requested: 1, // Specify how many tokens to consume\n    });\n\n    if (decision.isDenied()) {\n      if (decision.reason.isRateLimit()) {\n        const { remaining, reset } = decision.reason;\n        console.error({\n          code: \"RATE_LIMIT_EXCEEDED\",\n          details: {\n            remaining,\n            resetInSeconds: reset,\n          },\n        });\n\n        throw new Error(\"Too many requests. Please try again later.\");\n      }\n\n      throw new Error(\"Request blocked\");\n    }\n\n    const user = await db.user.findUnique({\n      where: { clerkUserId: userId },\n    });\n\n    if (!user) {\n      throw new Error(\"User not found\");\n    }\n\n    // Convert balance to float before saving\n    const balanceFloat = parseFloat(data.balance);\n    if (isNaN(balanceFloat)) {\n      throw new Error(\"Invalid balance amount\");\n    }\n\n    // Check if this is the user's first account\n    const existingAccounts = await db.account.findMany({\n      where: { userId: user.id },\n    });\n\n    // If it's the first account, make it default regardless of user input\n    // If not, use the user's preference\n    const shouldBeDefault =\n      existingAccounts.length === 0 ? true : data.isDefault;\n\n    // If this account should be default, unset other default accounts\n    if (shouldBeDefault) {\n      await db.account.updateMany({\n        where: { userId: user.id, isDefault: true },\n        data: { isDefault: false },\n      });\n    }\n\n    // Create new account\n    const account = await db.account.create({\n      data: {\n        ...data,\n        balance: balanceFloat,\n        userId: user.id,\n        isDefault: shouldBeDefault, // Override the isDefault based on our logic\n      },\n    });\n\n    // Serialize the account before returning\n    const serializedAccount = serializeTransaction(account);\n\n    revalidatePath(\"/dashboard\");\n    return { success: true, data: serializedAccount };\n  } catch (error) {\n    throw new Error(error.message);\n  }\n}\n\nexport async function getDashboardData() {\n  const { userId } = await auth();\n  if (!userId) throw new Error(\"Unauthorized\");\n\n  const user = await db.user.findUnique({\n    where: { clerkUserId: userId },\n  });\n\n  if (!user) {\n    throw new Error(\"User not found\");\n  }\n\n  // Get all user transactions\n  const transactions = await db.transaction.findMany({\n    where: { userId: user.id },\n    orderBy: { date: \"desc\" },\n  });\n\n  return transactions.map(serializeTransaction);\n}\n"], "names": [], "mappings": ";;;;;;;IAmBsB;IAkCA;IAmFA", "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/lib/schema.js"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const accountSchema = z.object({\n  name: z.string().min(1, \"Name is required\"),\n  type: z.enum([\"CURRENT\", \"SAVINGS\"]),\n  balance: z.string().min(1, \"Initial balance is required\"),\n  isDefault: z.boolean().default(false),\n});\n\nexport const transactionSchema = z\n  .object({\n    type: z.enum([\"INCOME\", \"EXPENSE\"]),\n    amount: z.string().min(1, \"Amount is required\"),\n    description: z.string().optional(),\n    date: z.date({ required_error: \"Date is required\" }),\n    accountId: z.string().min(1, \"Account is required\"),\n    category: z.string().min(1, \"Category is required\"),\n    isRecurring: z.boolean().default(false),\n    recurringInterval: z\n      .enum([\"DAILY\", \"WEEKLY\", \"MONTHLY\", \"YEARLY\"])\n      .optional(),\n  })\n  .superRefine((data, ctx) => {\n    if (data.isRecurring && !data.recurringInterval) {\n      ctx.addIssue({\n        code: z.ZodIssueCode.custom,\n        message: \"Recurring interval is required for recurring transactions\",\n        path: [\"recurringInterval\"],\n      });\n    }\n  });\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;KAAU;IACnC,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,WAAW,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AACjC;AAEO,MAAM,oBAAoB,oIAAA,CAAA,IAAC,CAC/B,MAAM,CAAC;IACN,MAAM,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAU;IAClC,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,MAAM,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAE,gBAAgB;IAAmB;IAClD,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,aAAa,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACjC,mBAAmB,oIAAA,CAAA,IAAC,CACjB,IAAI,CAAC;QAAC;QAAS;QAAU;QAAW;KAAS,EAC7C,QAAQ;AACb,GACC,WAAW,CAAC,CAAC,MAAM;IAClB,IAAI,KAAK,WAAW,IAAI,CAAC,KAAK,iBAAiB,EAAE;QAC/C,IAAI,QAAQ,CAAC;YACX,MAAM,oIAAA,CAAA,IAAC,CAAC,YAAY,CAAC,MAAM;YAC3B,SAAS;YACT,MAAM;gBAAC;aAAoB;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/create-account-drawer.jsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Loader2 } from \"lucide-react\";\nimport useFetch from \"@/hooks/use-fetch\";\nimport { toast } from \"sonner\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  Drawer,\n  DrawerContent,\n  Drawer<PERSON>eader,\n  DrawerTitle,\n  DrawerTrigger,\n  DrawerClose,\n} from \"@/components/ui/drawer\";\nimport { Input } from \"@/components/ui/input\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { createAccount } from \"@/actions/dashboard\";\nimport { accountSchema } from \"@/app/lib/schema\";\n\nexport function CreateAccountDrawer({ children }) {\n  const [open, setOpen] = useState(false);\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    setValue,\n    watch,\n    reset,\n  } = useForm({\n    resolver: zodResolver(accountSchema),\n    defaultValues: {\n      name: \"\",\n      type: \"CURRENT\",\n      balance: \"\",\n      isDefault: false,\n    },\n  });\n\n  const {\n    loading: createAccountLoading,\n    fn: createAccountFn,\n    error,\n    data: newAccount,\n  } = useFetch(createAccount);\n\n  const onSubmit = async (data) => {\n    await createAccountFn(data);\n  };\n\n  useEffect(() => {\n    if (newAccount) {\n      toast.success(\"Account created successfully\");\n      reset();\n      setOpen(false);\n    }\n  }, [newAccount, reset]);\n\n  useEffect(() => {\n    if (error) {\n      toast.error(error.message || \"Failed to create account\");\n    }\n  }, [error]);\n\n  return (\n    <Drawer open={open} onOpenChange={setOpen}>\n      <DrawerTrigger asChild>{children}</DrawerTrigger>\n      <DrawerContent>\n        <DrawerHeader>\n          <DrawerTitle>Create New Account</DrawerTitle>\n        </DrawerHeader>\n        <div className=\"px-4 pb-4\">\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n            <div className=\"space-y-2\">\n              <label\n                htmlFor=\"name\"\n                className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n              >\n                Account Name\n              </label>\n              <Input\n                id=\"name\"\n                placeholder=\"e.g., Main Checking\"\n                {...register(\"name\")}\n              />\n              {errors.name && (\n                <p className=\"text-sm text-red-500\">{errors.name.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <label\n                htmlFor=\"type\"\n                className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n              >\n                Account Type\n              </label>\n              <Select\n                onValueChange={(value) => setValue(\"type\", value)}\n                defaultValue={watch(\"type\")}\n              >\n                <SelectTrigger id=\"type\">\n                  <SelectValue placeholder=\"Select type\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"CURRENT\">Current</SelectItem>\n                  <SelectItem value=\"SAVINGS\">Savings</SelectItem>\n                </SelectContent>\n              </Select>\n              {errors.type && (\n                <p className=\"text-sm text-red-500\">{errors.type.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <label\n                htmlFor=\"balance\"\n                className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n              >\n                Initial Balance\n              </label>\n              <Input\n                id=\"balance\"\n                type=\"number\"\n                step=\"0.01\"\n                placeholder=\"0.00\"\n                {...register(\"balance\")}\n              />\n              {errors.balance && (\n                <p className=\"text-sm text-red-500\">{errors.balance.message}</p>\n              )}\n            </div>\n\n            <div className=\"flex items-center justify-between rounded-lg border p-3\">\n              <div className=\"space-y-0.5\">\n                <label\n                  htmlFor=\"isDefault\"\n                  className=\"text-base font-medium cursor-pointer\"\n                >\n                  Set as Default\n                </label>\n                <p className=\"text-sm text-muted-foreground\">\n                  This account will be selected by default for transactions\n                </p>\n              </div>\n              <Switch\n                id=\"isDefault\"\n                checked={watch(\"isDefault\")}\n                onCheckedChange={(checked) => setValue(\"isDefault\", checked)}\n              />\n            </div>\n\n            <div className=\"flex gap-4 pt-4\">\n              <DrawerClose asChild>\n                <Button type=\"button\" variant=\"outline\" className=\"flex-1\">\n                  Cancel\n                </Button>\n              </DrawerClose>\n              <Button\n                type=\"submit\"\n                className=\"flex-1\"\n                disabled={createAccountLoading}\n              >\n                {createAccountLoading ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Creating...\n                  </>\n                ) : (\n                  \"Create Account\"\n                )}\n              </Button>\n            </div>\n          </form>\n        </div>\n      </DrawerContent>\n    </Drawer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAQA;AACA;AAOA;AACA;AACA;AA5BA;;;;;;;;;;;;;;;AA8BO,SAAS,oBAAoB,EAAE,QAAQ,EAAE;IAC9C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE;QACV,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,oHAAA,CAAA,gBAAa;QACnC,eAAe;YACb,MAAM;YACN,MAAM;YACN,SAAS;YACT,WAAW;QACb;IACF;IAEA,MAAM,EACJ,SAAS,oBAAoB,EAC7B,IAAI,eAAe,EACnB,KAAK,EACL,MAAM,UAAU,EACjB,GAAG,CAAA,GAAA,qHAAA,CAAA,UAAQ,AAAD,EAAE,oHAAA,CAAA,gBAAa;IAE1B,MAAM,WAAW,OAAO;QACtB,MAAM,gBAAgB;IACxB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,QAAQ;QACV;IACF,GAAG;QAAC;QAAY;KAAM;IAEtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAChC,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,OAAO;0BAAE;;;;;;0BACxB,8OAAC,2HAAA,CAAA,gBAAa;;kCACZ,8OAAC,2HAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,2HAAA,CAAA,cAAW;sCAAC;;;;;;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,UAAU,aAAa;4BAAW,WAAU;;8CAChD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,8OAAC,0HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACX,GAAG,SAAS,OAAO;;;;;;wCAErB,OAAO,IAAI,kBACV,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;8CAI5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,8OAAC,2HAAA,CAAA,SAAM;4CACL,eAAe,CAAC,QAAU,SAAS,QAAQ;4CAC3C,cAAc,MAAM;;8DAEpB,8OAAC,2HAAA,CAAA,gBAAa;oDAAC,IAAG;8DAChB,cAAA,8OAAC,2HAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,2HAAA,CAAA,gBAAa;;sEACZ,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;;;;;;;;;;;;;wCAG/B,OAAO,IAAI,kBACV,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;8CAI5D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,8OAAC,0HAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,aAAY;4CACX,GAAG,SAAS,UAAU;;;;;;wCAExB,OAAO,OAAO,kBACb,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAI/D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAQ;oDACR,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAI/C,8OAAC,2HAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS,MAAM;4CACf,iBAAiB,CAAC,UAAY,SAAS,aAAa;;;;;;;;;;;;8CAIxD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2HAAA,CAAA,cAAW;4CAAC,OAAO;sDAClB,cAAA,8OAAC,2HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAQ;gDAAU,WAAU;0DAAS;;;;;;;;;;;sDAI7D,8OAAC,2HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU;sDAET,qCACC;;kEACE,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;+DAInD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 932, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/actions/account.js"], "sourcesContent": ["\"use server\";\n\nimport { db } from \"@/lib/prisma\";\nimport { auth } from \"@clerk/nextjs/server\";\nimport { revalidatePath } from \"next/cache\";\n\nconst serializeDecimal = (obj) => {\n  const serialized = { ...obj };\n  if (obj.balance) {\n    serialized.balance = obj.balance.toNumber();\n  }\n  if (obj.amount) {\n    serialized.amount = obj.amount.toNumber();\n  }\n  return serialized;\n};\n\nexport async function getAccountWithTransactions(accountId) {\n  const { userId } = await auth();\n  if (!userId) throw new Error(\"Unauthorized\");\n\n  const user = await db.user.findUnique({\n    where: { clerkUserId: userId },\n  });\n\n  if (!user) throw new Error(\"User not found\");\n\n  const account = await db.account.findUnique({\n    where: {\n      id: accountId,\n      userId: user.id,\n    },\n    include: {\n      transactions: {\n        orderBy: { date: \"desc\" },\n      },\n      _count: {\n        select: { transactions: true },\n      },\n    },\n  });\n\n  if (!account) return null;\n\n  return {\n    ...serializeDecimal(account),\n    transactions: account.transactions.map(serializeDecimal),\n  };\n}\n\nexport async function bulkDeleteTransactions(transactionIds) {\n  try {\n    const { userId } = await auth();\n    if (!userId) throw new Error(\"Unauthorized\");\n\n    const user = await db.user.findUnique({\n      where: { clerkUserId: userId },\n    });\n\n    if (!user) throw new Error(\"User not found\");\n\n    // Get transactions to calculate balance changes\n    const transactions = await db.transaction.findMany({\n      where: {\n        id: { in: transactionIds },\n        userId: user.id,\n      },\n    });\n\n    // Group transactions by account to update balances\n    const accountBalanceChanges = transactions.reduce((acc, transaction) => {\n      const change =\n        transaction.type === \"EXPENSE\"\n          ? transaction.amount\n          : -transaction.amount;\n      acc[transaction.accountId] = (acc[transaction.accountId] || 0) + change;\n      return acc;\n    }, {});\n\n    // Delete transactions and update account balances in a transaction\n    await db.$transaction(async (tx) => {\n      // Delete transactions\n      await tx.transaction.deleteMany({\n        where: {\n          id: { in: transactionIds },\n          userId: user.id,\n        },\n      });\n\n      // Update account balances\n      for (const [accountId, balanceChange] of Object.entries(\n        accountBalanceChanges\n      )) {\n        await tx.account.update({\n          where: { id: accountId },\n          data: {\n            balance: {\n              increment: balanceChange,\n            },\n          },\n        });\n      }\n    });\n\n    revalidatePath(\"/dashboard\");\n    revalidatePath(\"/account/[id]\");\n\n    return { success: true };\n  } catch (error) {\n    return { success: false, error: error.message };\n  }\n}\n\nexport async function updateDefaultAccount(accountId) {\n  try {\n    const { userId } = await auth();\n    if (!userId) throw new Error(\"Unauthorized\");\n\n    const user = await db.user.findUnique({\n      where: { clerkUserId: userId },\n    });\n\n    if (!user) {\n      throw new Error(\"User not found\");\n    }\n\n    // First, unset any existing default account\n    await db.account.updateMany({\n      where: {\n        userId: user.id,\n        isDefault: true,\n      },\n      data: { isDefault: false },\n    });\n\n    // Then set the new default account\n    const account = await db.account.update({\n      where: {\n        id: accountId,\n        userId: user.id,\n      },\n      data: { isDefault: true },\n    });\n\n    revalidatePath(\"/dashboard\");\n    return { success: true, data: serializeTransaction(account) };\n  } catch (error) {\n    return { success: false, error: error.message };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;IAiBsB;IAiCA;IA+DA", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/card.jsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"rounded-xl border bg-card text-card-foreground shadow\", className)}\n    {...props} />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props} />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props} />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACtD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;QACtE,GAAG,KAAK;;;;;;AAEb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAEb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACjE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAEb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/_components/account-card.jsx"], "sourcesContent": ["\"use client\";\n\nimport { updateDefaultAccount } from \"@/actions/account\";\nimport {\n    <PERSON>,\n    <PERSON><PERSON><PERSON><PERSON>,\n    <PERSON><PERSON><PERSON><PERSON>,\n    Card<PERSON><PERSON>er,\n    CardTitle,\n} from \"@/components/ui/card\";\nimport { Switch } from \"@/components/ui/switch\";\nimport useFetch from \"@/hooks/use-fetch\";\nimport { ArrowDownRight, ArrowUpRight } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useEffect } from \"react\";\nimport { toast } from \"sonner\";\n\nexport function AccountCard({ account }) {\n  const { name, type, balance, id, isDefault } = account;\n\n  const {\n    loading: updateDefaultLoading,\n    fn: updateDefaultFn,\n    data: updatedAccount,\n    error,\n  } = useFetch(updateDefaultAccount);\n\n  const handleDefaultChange = async (event) => {\n    event.preventDefault(); // Prevent navigation\n\n    if (isDefault) {\n      toast.warning(\"You need atleast 1 default account\");\n      return; // Don't allow toggling off the default account\n    }\n\n    await updateDefaultFn(id);\n  };\n\n  useEffect(() => {\n    if (updatedAccount?.success) {\n      toast.success(\"Default account updated successfully\");\n    }\n  }, [updatedAccount]);\n\n  useEffect(() => {\n    if (error) {\n      toast.error(error.message || \"Failed to update default account\");\n    }\n  }, [error]);\n\n  return (\n    <Card className=\"hover:shadow-md transition-shadow group relative\">\n      <Link href={`/account/${id}`}>\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n          <CardTitle className=\"text-sm font-medium capitalize\">\n            {name}\n          </CardTitle>\n          <Switch\n            checked={isDefault}\n            onClick={handleDefaultChange}\n            disabled={updateDefaultLoading}\n          />\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-2xl font-bold\">\n            ₹{parseFloat(balance).toFixed(2)}\n          </div>\n          <p className=\"text-xs text-muted-foreground\">\n            {type.charAt(0) + type.slice(1).toLowerCase()} Account\n          </p>\n        </CardContent>\n        <CardFooter className=\"flex justify-between text-sm text-muted-foreground\">\n          <div className=\"flex items-center\">\n            <ArrowUpRight className=\"mr-1 h-4 w-4 text-green-500\" />\n            Income\n          </div>\n          <div className=\"flex items-center\">\n            <ArrowDownRight className=\"mr-1 h-4 w-4 text-red-500\" />\n            Expense\n          </div>\n        </CardFooter>\n      </Link>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AAAA;AACA;AACA;AACA;AAfA;;;;;;;;;;AAiBO,SAAS,YAAY,EAAE,OAAO,EAAE;IACrC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG;IAE/C,MAAM,EACJ,SAAS,oBAAoB,EAC7B,IAAI,eAAe,EACnB,MAAM,cAAc,EACpB,KAAK,EACN,GAAG,CAAA,GAAA,qHAAA,CAAA,UAAQ,AAAD,EAAE,kHAAA,CAAA,uBAAoB;IAEjC,MAAM,sBAAsB,OAAO;QACjC,MAAM,cAAc,IAAI,qBAAqB;QAE7C,IAAI,WAAW;YACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,QAAQ,+CAA+C;QACzD;QAEA,MAAM,gBAAgB;IACxB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,SAAS;YAC3B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF,GAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,SAAS,EAAE,IAAI;;8BAC1B,8OAAC,yHAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;sCAClB;;;;;;sCAEH,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAS;4BACT,UAAU;;;;;;;;;;;;8BAGd,8OAAC,yHAAA,CAAA,cAAW;;sCACV,8OAAC;4BAAI,WAAU;;gCAAqB;gCAChC,WAAW,SAAS,OAAO,CAAC;;;;;;;sCAEhC,8OAAC;4BAAE,WAAU;;gCACV,KAAK,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,WAAW;gCAAG;;;;;;;;;;;;;8BAGlD,8OAAC,yHAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCAAgC;;;;;;;sCAG1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;;;;;;;;;;;;;;;;;;AAOpE", "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/components/ui/progress.jsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Progress = React.forwardRef(\n  ({ className, value, extraStyles, ...props }, ref) => (\n    <ProgressPrimitive.Root\n      ref={ref}\n      className={cn(\n        \"relative h-2 w-full overflow-hidden rounded-full bg-primary/20\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        className={`h-full w-full flex-1 bg-primary transition-all ${extraStyles}`}\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n);\nProgress.displayName = ProgressPrimitive.Root.displayName;\n\nexport { Progress };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,OAAO,EAAE,oBAC5C,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAC,+CAA+C,EAAE,aAAa;YAC1E,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAKlE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1238, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/actions/budget.js"], "sourcesContent": ["\"use server\";\n\nimport { db } from \"@/lib/prisma\";\nimport { auth } from \"@clerk/nextjs/server\";\nimport { revalidatePath } from \"next/cache\";\n\nexport async function getCurrentBudget(accountId) {\n  try {\n    const { userId } = await auth();\n    if (!userId) throw new Error(\"Unauthorized\");\n\n    const user = await db.user.findUnique({\n      where: { clerkUserId: userId },\n    });\n\n    if (!user) {\n      throw new Error(\"User not found\");\n    }\n\n    const budget = await db.budget.findFirst({\n      where: {\n        userId: user.id,\n      },\n    });\n\n    // Get current month's expenses\n    const currentDate = new Date();\n    const startOfMonth = new Date(\n      currentDate.getFullYear(),\n      currentDate.getMonth(),\n      1\n    );\n    const endOfMonth = new Date(\n      currentDate.getFullYear(),\n      currentDate.getMonth() + 1,\n      0\n    );\n\n    const expenses = await db.transaction.aggregate({\n      where: {\n        userId: user.id,\n        type: \"EXPENSE\",\n        date: {\n          gte: startOfMonth,\n          lte: endOfMonth,\n        },\n        accountId,\n      },\n      _sum: {\n        amount: true,\n      },\n    });\n\n    return {\n      budget: budget ? { ...budget, amount: budget.amount.toNumber() } : null,\n      currentExpenses: expenses._sum.amount\n        ? expenses._sum.amount.toNumber()\n        : 0,\n    };\n  } catch (error) {\n    console.error(\"Error fetching budget:\", error);\n    throw error;\n  }\n}\n\nexport async function updateBudget(amount) {\n  try {\n    const { userId } = await auth();\n    if (!userId) throw new Error(\"Unauthorized\");\n\n    const user = await db.user.findUnique({\n      where: { clerkUserId: userId },\n    });\n\n    if (!user) throw new Error(\"User not found\");\n\n    // Update or create budget\n    const budget = await db.budget.upsert({\n      where: {\n        userId: user.id,\n      },\n      update: {\n        amount,\n      },\n      create: {\n        userId: user.id,\n        amount,\n      },\n    });\n\n    revalidatePath(\"/dashboard\");\n    return {\n      success: true,\n      data: { ...budget, amount: budget.amount.toNumber() },\n    };\n  } catch (error) {\n    console.error(\"Error updating budget:\", error);\n    return { success: false, error: error.message };\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAMsB;IA2DA", "debugId": null}}, {"offset": {"line": 1252, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/_components/budget-progress.jsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Pencil, Check, X } from \"lucide-react\";\nimport useFetch from \"@/hooks/use-fetch\";\nimport { toast } from \"sonner\";\n\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { updateBudget } from \"@/actions/budget\";\n\nexport function BudgetProgress({ initialBudget, currentExpenses }) {\n  const [isEditing, setIsEditing] = useState(false);\n  const [newBudget, setNewBudget] = useState(\n    initialBudget?.amount?.toString() || \"\"\n  );\n\n  const {\n    loading: isLoading,\n    fn: updateBudgetFn,\n    data: updatedBudget,\n    error,\n  } = useFetch(updateBudget);\n\n  const percentUsed = initialBudget\n    ? (currentExpenses / initialBudget.amount) * 100\n    : 0;\n\n  const handleUpdateBudget = async () => {\n    const amount = parseFloat(newBudget);\n\n    if (isNaN(amount) || amount <= 0) {\n      toast.error(\"Please enter a valid amount\");\n      return;\n    }\n\n    await updateBudgetFn(amount);\n  };\n\n  const handleCancel = () => {\n    setNewBudget(initialBudget?.amount?.toString() || \"\");\n    setIsEditing(false);\n  };\n\n  useEffect(() => {\n    if (updatedBudget?.success) {\n      setIsEditing(false);\n      toast.success(\"Budget updated successfully\");\n    }\n  }, [updatedBudget]);\n\n  useEffect(() => {\n    if (error) {\n      toast.error(error.message || \"Failed to update budget\");\n    }\n  }, [error]);\n\n  return (\n    <Card>\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n        <div className=\"flex-1\">\n          <CardTitle className=\"text-sm font-medium\">\n            Monthly Budget (Default Account)\n          </CardTitle>\n          <div className=\"flex items-center gap-2 mt-1\">\n            {isEditing ? (\n              <div className=\"flex items-center gap-2\">\n                <Input\n                  type=\"number\"\n                  value={newBudget}\n                  onChange={(e) => setNewBudget(e.target.value)}\n                  className=\"w-32\"\n                  placeholder=\"Enter amount\"\n                  autoFocus\n                  disabled={isLoading}\n                />\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={handleUpdateBudget}\n                  disabled={isLoading}\n                >\n                  <Check className=\"h-4 w-4 text-green-500\" />\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={handleCancel}\n                  disabled={isLoading}\n                >\n                  <X className=\"h-4 w-4 text-red-500\" />\n                </Button>\n              </div>\n            ) : (\n              <>\n                <CardDescription>\n                  {initialBudget\n                    ? `₹${currentExpenses.toFixed(\n                        2\n                      )} of ₹${initialBudget.amount.toFixed(2)} spent`\n                    : \"No budget set\"}\n                </CardDescription>\n                <Button\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  onClick={() => setIsEditing(true)}\n                  className=\"h-6 w-6\"\n                >\n                  <Pencil className=\"h-3 w-3\" />\n                </Button>\n              </>\n            )}\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {initialBudget && (\n          <div className=\"space-y-2\">\n            <Progress\n              value={percentUsed}\n              extraStyles={`${\n                // add to Progress component\n                percentUsed >= 90\n                  ? \"bg-red-500\"\n                  : percentUsed >= 75\n                    ? \"bg-yellow-500\"\n                    : \"bg-green-500\"\n              }`}\n            />\n            <p className=\"text-xs text-muted-foreground text-right\">\n              {percentUsed.toFixed(1)}% used\n            </p>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AAOA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;AAmBO,SAAS,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvC,eAAe,QAAQ,cAAc;IAGvC,MAAM,EACJ,SAAS,SAAS,EAClB,IAAI,cAAc,EAClB,MAAM,aAAa,EACnB,KAAK,EACN,GAAG,CAAA,GAAA,qHAAA,CAAA,UAAQ,AAAD,EAAE,iHAAA,CAAA,eAAY;IAEzB,MAAM,cAAc,gBAChB,AAAC,kBAAkB,cAAc,MAAM,GAAI,MAC3C;IAEJ,MAAM,qBAAqB;QACzB,MAAM,SAAS,WAAW;QAE1B,IAAI,MAAM,WAAW,UAAU,GAAG;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,eAAe;IACvB;IAEA,MAAM,eAAe;QACnB,aAAa,eAAe,QAAQ,cAAc;QAClD,aAAa;IACf;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,SAAS;YAC1B,aAAa;YACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B;IACF,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC,yHAAA,CAAA,OAAI;;0BACH,8OAAC,yHAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;sCAAsB;;;;;;sCAG3C,8OAAC;4BAAI,WAAU;sCACZ,0BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;wCACV,aAAY;wCACZ,SAAS;wCACT,UAAU;;;;;;kDAEZ,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;kDAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;qDAIjB;;kDACE,8OAAC,yHAAA,CAAA,kBAAe;kDACb,gBACG,CAAC,CAAC,EAAE,gBAAgB,OAAO,CACzB,GACA,KAAK,EAAE,cAAc,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,GAChD;;;;;;kDAEN,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,8OAAC,yHAAA,CAAA,cAAW;0BACT,+BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,WAAQ;4BACP,OAAO;4BACP,aAAa,GACX,4BAA4B;4BAC5B,eAAe,KACX,eACA,eAAe,KACb,kBACA,gBACN;;;;;;sCAEJ,8OAAC;4BAAE,WAAU;;gCACV,YAAY,OAAO,CAAC;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/SEM%206%20NEW%20Project/2%20AIFin/AIfin/ai-finance-platform-main/app/%28main%29/dashboard/_components/transaction-overview.jsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  Cell,\n  ResponsiveContainer,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n} from \"recharts\";\nimport { format } from \"date-fns\";\nimport { ArrowUpRight, ArrowDownRight } from \"lucide-react\";\n\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { cn } from \"@/lib/utils\";\n\nconst COLORS = [\n  \"#FF6B6B\",\n  \"#4ECDC4\",\n  \"#45B7D1\",\n  \"#96CEB4\",\n  \"#FFEEAD\",\n  \"#D4A5A5\",\n  \"#9FA8DA\",\n];\n\nexport function DashboardOverview({ accounts, transactions }) {\n  const [selectedAccountId, setSelectedAccountId] = useState(\n    accounts.find((a) => a.isDefault)?.id || accounts[0]?.id\n  );\n  const [timePeriod, setTimePeriod] = useState(\"Monthly\");\n\n  // Filter transactions for selected account\n  const accountTransactions = transactions.filter(\n    (t) => t.accountId === selectedAccountId\n  );\n\n  // Get recent transactions (last 5)\n  const recentTransactions = accountTransactions\n    .sort((a, b) => new Date(b.date) - new Date(a.date))\n    .slice(0, 5);\n\n  // Filter expenses based on selected time period\n  const filterExpensesByTimePeriod = () => {\n    const currentDate = new Date();\n    return accountTransactions.filter((t) => {\n      if (t.type !== \"EXPENSE\") return false;\n      const transactionDate = new Date(t.date);\n\n      if (timePeriod === \"Daily\") {\n        return (\n          transactionDate.toDateString() === currentDate.toDateString()\n        );\n      } else if (timePeriod === \"Monthly\") {\n        return (\n          transactionDate.getMonth() === currentDate.getMonth() &&\n          transactionDate.getFullYear() === currentDate.getFullYear()\n        );\n      } else if (timePeriod === \"6 Months\") {\n        const sixMonthsAgo = new Date();\n        sixMonthsAgo.setMonth(currentDate.getMonth() - 6);\n        return transactionDate >= sixMonthsAgo;\n      }\n      return true;\n    });\n  };\n\n  const filteredExpenses = filterExpensesByTimePeriod();\n\n  // Group expenses by category\n  const expensesByCategory = filteredExpenses.reduce((acc, transaction) => {\n    const category = transaction.category;\n    if (!acc[category]) {\n      acc[category] = 0;\n    }\n    acc[category] += transaction.amount;\n    return acc;\n  }, {});\n\n  // Format data for pie chart\n  const pieChartData = Object.entries(expensesByCategory).map(\n    ([category, amount]) => ({\n      name: category,\n      value: amount,\n    })\n  );\n\n  return (\n    <div className=\"grid gap-4 md:grid-cols-2\">\n      {/* Recent Transactions Card */}\n      <Card>\n        <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-4\">\n          <CardTitle className=\"text-base font-normal\">\n            Recent Transactions\n          </CardTitle>\n          <Select\n            value={selectedAccountId}\n            onValueChange={setSelectedAccountId}\n          >\n            <SelectTrigger className=\"w-[140px]\">\n              <SelectValue placeholder=\"Select account\" />\n            </SelectTrigger>\n            <SelectContent>\n              {accounts.map((account) => (\n                <SelectItem key={account.id} value={account.id}>\n                  {account.name}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {recentTransactions.length === 0 ? (\n              <p className=\"text-center text-muted-foreground py-4\">\n                No recent transactions\n              </p>\n            ) : (\n              recentTransactions.map((transaction) => (\n                <div\n                  key={transaction.id}\n                  className=\"flex items-center justify-between\"\n                >\n                  <div className=\"space-y-1\">\n                    <p className=\"text-sm font-medium leading-none\">\n                      {transaction.description || \"Untitled Transaction\"}\n                    </p>\n                    <p className=\"text-sm text-muted-foreground\">\n                      {format(new Date(transaction.date), \"PP\")}\n                    </p>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <div\n                      className={cn(\n                        \"flex items-center\",\n                        transaction.type === \"EXPENSE\"\n                          ? \"text-red-500\"\n                          : \"text-green-500\"\n                      )}\n                    >\n                      {transaction.type === \"EXPENSE\" ? (\n                        <ArrowDownRight className=\"mr-1 h-4 w-4\" />\n                      ) : (\n                        <ArrowUpRight className=\"mr-1 h-4 w-4\" />\n                      )}\n                      ₹{transaction.amount.toFixed(2)}\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Expense Breakdown Card */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-base font-normal\">\n            Expense Breakdown\n          </CardTitle>\n          <div className=\"flex gap-2 mt-2\">\n            {[\"Daily\", \"Monthly\", \"6 Months\", \"All Time\"].map((period) => (\n              <button\n                key={period}\n                className={`px-3 py-1 border rounded-md ${\n                  timePeriod === period ? \"bg-blue-500 text-white\" : \"bg-gray-200\"\n                }`}\n                onClick={() => setTimePeriod(period)}\n              >\n                {period}\n              </button>\n            ))\n            }\n          </div>\n        </CardHeader>\n        <CardContent className=\"p-0 pb-5\">\n          {pieChartData.length === 0 ? (\n            <p className=\"text-center text-muted-foreground py-4\">\n              No expenses in selected period\n            </p>\n          ) : (\n            <div className=\"h-[300px]\">\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <PieChart>\n                  <Pie\n                    data={pieChartData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    outerRadius={80}\n                    fill=\"#8884d8\"\n                    dataKey=\"value\"\n                    label={({ name, value }) => `${name}: ₹${value.toFixed(2)}`}\n                  >\n                    {pieChartData.map((entry, index) => (\n                      <Cell\n                        key={`cell-${index}`}\n                        fill={COLORS[index % COLORS.length]}\n                      />\n                    ))}\n                  </Pie>\n                  <Tooltip formatter={(value) => `₹${value.toFixed(2)}`} />\n                  <Legend />\n                </PieChart>\n              </ResponsiveContainer>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAAA;AAEA;AAOA;AACA;AAtBA;;;;;;;;;AAwBA,MAAM,SAAS;IACb;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,kBAAkB,EAAE,QAAQ,EAAE,YAAY,EAAE;IAC1D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,SAAS,GAAG,MAAM,QAAQ,CAAC,EAAE,EAAE;IAExD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,2CAA2C;IAC3C,MAAM,sBAAsB,aAAa,MAAM,CAC7C,CAAC,IAAM,EAAE,SAAS,KAAK;IAGzB,mCAAmC;IACnC,MAAM,qBAAqB,oBACxB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,GACjD,KAAK,CAAC,GAAG;IAEZ,gDAAgD;IAChD,MAAM,6BAA6B;QACjC,MAAM,cAAc,IAAI;QACxB,OAAO,oBAAoB,MAAM,CAAC,CAAC;YACjC,IAAI,EAAE,IAAI,KAAK,WAAW,OAAO;YACjC,MAAM,kBAAkB,IAAI,KAAK,EAAE,IAAI;YAEvC,IAAI,eAAe,SAAS;gBAC1B,OACE,gBAAgB,YAAY,OAAO,YAAY,YAAY;YAE/D,OAAO,IAAI,eAAe,WAAW;gBACnC,OACE,gBAAgB,QAAQ,OAAO,YAAY,QAAQ,MACnD,gBAAgB,WAAW,OAAO,YAAY,WAAW;YAE7D,OAAO,IAAI,eAAe,YAAY;gBACpC,MAAM,eAAe,IAAI;gBACzB,aAAa,QAAQ,CAAC,YAAY,QAAQ,KAAK;gBAC/C,OAAO,mBAAmB;YAC5B;YACA,OAAO;QACT;IACF;IAEA,MAAM,mBAAmB;IAEzB,6BAA6B;IAC7B,MAAM,qBAAqB,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACvD,MAAM,WAAW,YAAY,QAAQ;QACrC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;YAClB,GAAG,CAAC,SAAS,GAAG;QAClB;QACA,GAAG,CAAC,SAAS,IAAI,YAAY,MAAM;QACnC,OAAO;IACT,GAAG,CAAC;IAEJ,4BAA4B;IAC5B,MAAM,eAAe,OAAO,OAAO,CAAC,oBAAoB,GAAG,CACzD,CAAC,CAAC,UAAU,OAAO,GAAK,CAAC;YACvB,MAAM;YACN,OAAO;QACT,CAAC;IAGH,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;0CAAwB;;;;;;0CAG7C,8OAAC,2HAAA,CAAA,SAAM;gCACL,OAAO;gCACP,eAAe;;kDAEf,8OAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,2HAAA,CAAA,gBAAa;kDACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,2HAAA,CAAA,aAAU;gDAAkB,OAAO,QAAQ,EAAE;0DAC3C,QAAQ,IAAI;+CADE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAOnC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,mBAAmB,MAAM,KAAK,kBAC7B,8OAAC;gCAAE,WAAU;0CAAyC;;;;;uCAItD,mBAAmB,GAAG,CAAC,CAAC,4BACtB,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,YAAY,WAAW,IAAI;;;;;;8DAE9B,8OAAC;oDAAE,WAAU;8DACV,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY,IAAI,GAAG;;;;;;;;;;;;sDAGxC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qBACA,YAAY,IAAI,KAAK,YACjB,iBACA;;oDAGL,YAAY,IAAI,KAAK,0BACpB,8OAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;6EAE1B,8OAAC,0NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDACxB;oDACA,YAAY,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;mCAzB5B,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;0BAoC/B,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;0CAAwB;;;;;;0CAG7C,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAS;oCAAW;oCAAY;iCAAW,CAAC,GAAG,CAAC,CAAC,uBACjD,8OAAC;wCAEC,WAAW,CAAC,4BAA4B,EACtC,eAAe,SAAS,2BAA2B,eACnD;wCACF,SAAS,IAAM,cAAc;kDAE5B;uCANI;;;;;;;;;;;;;;;;kCAYb,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,aAAa,MAAM,KAAK,kBACvB,8OAAC;4BAAE,WAAU;sCAAyC;;;;;iDAItD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAO;0CACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;sDACP,8OAAC,+IAAA,CAAA,MAAG;4CACF,MAAM;4CACN,IAAG;4CACH,IAAG;4CACH,aAAa;4CACb,MAAK;4CACL,SAAQ;4CACR,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,GAAG,KAAK,GAAG,EAAE,MAAM,OAAO,CAAC,IAAI;sDAE1D,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC,oJAAA,CAAA,OAAI;oDAEH,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;mDAD9B,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;sDAK1B,8OAAC,uJAAA,CAAA,UAAO;4CAAC,WAAW,CAAC,QAAU,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,IAAI;;;;;;sDACrD,8OAAC,sJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzB", "debugId": null}}]}